{#
/**
 * @file
 * Default theme implementation for a video file element.
 *
 * Available variables:
 * - element: The element.
 * - value: The element's value.
 * - options Associative array of options for element.
 * - file: The element's File object.
 * - file_link: Link to the file.
 *
 * @see http://caniuse.com/#feat=video
 * @see https://developer.mozilla.org/en-US/docs/Web/HTML/Element/video
 */
#}
{% if extension == 'mp4' %}
  {{ attach_library('webform/webform.element.video_file') }}
  <div class="webform-video-file">
    <video width="620" height="349" controls>
      <source src="{{ uri }}" type="{{ type }}">
    </video>
  </div>
{% endif %}
<div>{{ file_link }}</div>
