/**
 * Colorbox Core Style:
 * The following CSS is consistent between example themes and should not be altered.
 */

/**
 * Disable stylelint rule 'selector-id-pattern' since these CSS IDs are provided
 * by the colorbox JS library and not defined by the module.
 */

/* stylelint-disable selector-id-pattern */
#colorbox,
#cboxOverlay,
#cboxWrapper {
  position: absolute;
  z-index: 9999;
  top: 0;
  left: 0;
  overflow: hidden;
}
#cboxOverlay {
  position: fixed;
  width: 100%;
  height: 100%;
}
#cboxMiddleLeft,
#cboxBottomLeft {
  clear: left;
}
#cboxContent {
  position: relative;
}
#cboxLoadedContent {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
#cboxTitle {
  margin: 0;
}
#cboxLoadingOverlay,
#cboxLoadingGraphic {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
/**
 * These elements are buttons, and may need to have additional
 * styles reset to avoid unwanted base styles.
 */
#cboxPrevious,
#cboxNext,
#cboxClose,
#cboxSlideshow {
  overflow: visible;
  width: auto;
  margin: 0;
  padding: 0;
  cursor: pointer;
  border: 0;
  background: none;
}
/**
 * Avoid outlines on :active (mouse click),
 * but preserve outlines on :focus (tabbed navigating)
 */
#cboxPrevious:active,
#cboxNext:active,
#cboxClose:active,
#cboxSlideshow:active {
  outline: 0;
}
.cboxPhoto {
  display: block;
  float: left;
  max-width: none;
  margin: auto;
  border: 0;
}
.cboxIframe {
  display: block;
  width: 100%;
  height: 100%;
  border: 0;
}
/* Reset box sizing to content-box if theme is using border-box. */
#colorbox,
#cboxContent,
#cboxLoadedContent {
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}

/**
 * Colorbox module Stockholm syndrome style:
 * The styles are ordered & tabbed in a way that represents
 * the nesting of the generated HTML.
 */
#cboxOverlay {
  background: #000;
}

#colorbox {
  -webkit-border-bottom-right-radius: 9px;
  border-bottom-right-radius: 9px;
  -webkit-border-bottom-left-radius: 9px;
  border-bottom-left-radius: 9px;
  outline: 0;
  background: #fff url(images/bg_tab.png) center bottom repeat-x;
  -moz-box-shadow: 3px 3px 16px #333;
  -webkit-box-shadow: 3px 3px 16px #333;
  box-shadow: 3px 3px 16px #333;
  -moz-border-radius-bottomleft: 9px;
  -moz-border-radius-bottomright: 9px;
}
#colorbox,
#colorbox div {
  overflow: visible; /* Required by the close button. */
}
#cboxWrapper {
  -webkit-border-bottom-right-radius: 9px;
  border-bottom-right-radius: 9px;
  -webkit-border-bottom-left-radius: 9px;
  border-bottom-left-radius: 9px;
  -moz-border-radius-bottomleft: 9px;
  -moz-border-radius-bottomright: 9px;
}
#cboxTopLeft {
  width: 0;
  height: 0;
}
#cboxTopCenter {
  height: 0;
}
#cboxTopRight {
  width: 0;
  height: 0;
}
#cboxBottomLeft {
  width: 15px;
  height: 10px;
}
#cboxBottomCenter {
  height: 10px;
}
#cboxBottomRight {
  width: 15px;
  height: 10px;
}
#cboxMiddleLeft {
  width: 0;
}
#cboxMiddleRight {
  width: 0;
}
#cboxContent {
  overflow: hidden;
  margin-bottom: 28px;
  background: #fff;
}
#cboxError {
  padding: 50px;
  border: 1px solid #ccc;
}
/* stylelint-disable-next-line block-no-empty */
#cboxLoadedContent {
}
#cboxTitle {
  left: 0;
  display: table-cell !important;
  float: none !important;
  height: 38px;
  padding: 0 140px 0 15px;
  vertical-align: middle;
  color: #313131;
}
#cboxCurrent {
  position: absolute;
  right: 80px;
  bottom: -26px;
  padding: 0 0 0 15px;
  color: #313131;
  border-left: 1px solid #313131;
}
/* Slideshow not implemented. */
.cboxSlideshow_on #cboxSlideshow {
  display: none;
}
/* stylelint-disable-next-line block-no-empty */
.cboxSlideshow_on #cboxSlideshow:hover {
}
.cboxSlideshow_off #cboxSlideshow {
  display: none;
}
/* stylelint-disable-next-line block-no-empty */
.cboxSlideshow_off #cboxSlideshow:hover {
}
#cboxPrevious {
  position: absolute;
  right: 45px;
  bottom: -26px;
  width: 21px;
  height: 15px;
  text-indent: -9999px;
  background: url(images/controls.png) no-repeat 0 -48px;
}
#cboxPrevious:hover {
  background-position: 0 -111px;
}
#cboxNext {
  position: absolute;
  right: 15px;
  bottom: -26px;
  width: 21px;
  height: 15px;
  text-indent: -9999px;
  background: url(images/controls.png) no-repeat 0 -29px;
}
#cboxNext:hover {
  background-position: 0 -92px;
}
#cboxLoadingOverlay {
  background: #e6e6e6;
}
#cboxLoadingGraphic {
  background: url(images/loading_animation.gif) no-repeat center center;
}
#cboxClose {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 25px;
  height: 25px;
  text-indent: -9999px;
  opacity: 0;
  background: url(images/controls.png) no-repeat 0 0;
}
#cboxClose:hover {
  background-position: 0 -63px;
}
