<?php

namespace Drupal\webform\Element;

use <PERSON><PERSON>al\Core\Datetime\Entity\DateFormat;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Render\Element;
use <PERSON><PERSON>al\Core\Render\Element\FormElement;

/**
 * Provides a webform element for time selection.
 *
 * @code
 * $form['time'] = [
 *   '#type' => 'webform_time',
 *   '#title' => $this->t('Time'),
 *   '#default_value' => '12:00 AM'
 * ];
 * @endcode
 *
 * @FormElement("webform_time")
 */
class WebformTime extends FormElement {

  /**
   * {@inheritdoc}
   */
  public function getInfo() {
    $class = get_class($this);
    return [
      '#input' => TRUE,
      '#theme' => 'input__webform_time',
      '#process' => [[$class, 'processWebformTime']],
      '#pre_render' => [[$class, 'preRenderWebformTime']],
      '#theme_wrappers' => ['form_element'],
      '#timepicker' => FALSE,
      '#time_format' => 'H:i',
      '#size' => 12,
      '#maxlength' => 12,
    ];
  }

  /**
   * {@inheritdoc}
   */
  public static function valueCallback(&$element, $input, FormStateInterface $form_state) {
    if ($input === FALSE) {
      // Set default value using GNU PHP date format.
      // @see https://www.gnu.org/software/tar/manual/html_chapter/tar_7.html#Date-input-formats.
      if (!empty($element['#default_value'])) {
        try {
          // Evaluate if the value can be time formatted, including relative
          // values like 'now' or '+2 hours'.
          new \DateTime($element['#default_value']);
        }
        catch (\Exception $exception) {
          \Drupal::messenger()->addError($exception->getMessage());
          return NULL;
        }
        $element['#default_value'] = static::formatTime('H:i', strtotime($element['#default_value']));
        return $element['#default_value'];
      }
      else {
        return NULL;
      }
    }

    return $input;
  }

  /**
   * Processes a time webform element.
   */
  public static function processWebformTime(&$element, FormStateInterface $form_state, &$complete_form) {
    // Add validate callback.
    $element += ['#element_validate' => []];
    array_unshift($element['#element_validate'], [get_called_class(), 'validateWebformTime']);

    $element['#attached']['library'][] = 'webform/webform.element.time';
    $element['#attributes']['data-webform-time-format'] = !empty($element['#time_format']) ? $element['#time_format'] : DateFormat::load('html_time')->getPattern();
    return $element;
  }

  /**
   * Webform element validation handler for #type 'webform_time'.
   *
   * Note that #required is validated by _form_valistatic::formatTime() already.
   */
  public static function validateWebformTime(&$element, FormStateInterface $form_state, &$complete_form) {
    $has_access = Element::isVisibleElement($element);

    $value = $element['#value'];
    if (trim($value) === '') {
      return;
    }

    $time = strtotime($value);

    // Make sure the submitted value is parsable and in the specified
    // custom format (ex: g:i A) or default format (ex: H:i).
    //
    // This accounts for time values generated by an HTML5 time input or
    // the jQuery Timepicker.
    //
    // @see \Drupal\webform\Plugin\WebformElement\DateBase::validateDate
    // @see https://github.com/jonthornton/jquery-timepicker
    // @see js/webform.element.time.js
    $is_valid_time = $time && (
      $value === static::formatTime('H:i', $time) ||
      $value === static::formatTime('H:i:s', $time) ||
      $value === static::formatTime($element['#time_format'], $time)
    );
    if (!$is_valid_time) {
      if ($has_access) {
        if (isset($element['#title'])) {
          $form_state->setError($element, t('%name must be a valid time.', ['%name' => $element['#title']]));
        }
        else {
          $form_state->setError($element);
        }
      }
      return;
    }

    $name = empty($element['#title']) ? $element['#parents'][0] : $element['#title'];
    $time_format = $element['#time_format'];

    // Check minute steps.
    if (!empty($element['#step'])) {
      // Covert step seconds to minutes.
      $step = ($element['#step'] / 60);
      // Get total minutes.
      [$hours, $minutes] = explode(':', $value);
      $total_minutes = (intval($hours) * 60) + intval($minutes);
      if ($step > 1 && $total_minutes % $step) {
        $t_args = [
          '%name' => $name,
          '%step' => $step,
        ];
        $form_state->setError($element, t('%name must be a valid time with intervals from the dropdown (%step min/s).', $t_args));
      }
    }

    // Ensure that the input is greater than the #min property, if set.
    if ($has_access && isset($element['#min'])) {
      $min = strtotime($element['#min']);
      if ($time < $min) {
        $form_state->setError($element, t('%name must be on or after %min.', [
          '%name' => $name,
          '%min' => static::formatTime($time_format, $min),
        ]));
      }
    }

    // Ensure that the input is less than the #max property, if set.
    if ($has_access && isset($element['#max'])) {
      $max = strtotime($element['#max']);
      if ($time > $max) {
        $form_state->setError($element, t('%name must be on or before %max.', [
          '%name' => $name,
          '%max' => static::formatTime($time_format, $max),
        ]));
      }
    }

    // Convert time to 'H:i:s' format.
    $value = static::formatTime('H:i:s', $time);
    $element['#time_format'] = 'H:i:s';
    $element['#value'] = $value;
    $form_state->setValueForElement($element, $value);
  }

  /**
   * Adds form-specific attributes to a 'date' #type element.
   *
   * @param array $element
   *   An associative array containing the properties of the element.
   *
   * @return array
   *   The $element with prepared variables ready for #theme 'input__time'.
   */
  public static function preRenderWebformTime(array $element) {
    if (!empty($element['#timepicker'])) {
      // Render simple text field that is converted to timepicker.
      $element['#attributes']['type'] = 'text';
      // Apply #time_format to #default_value.
      if (!empty($element['#value']) && !empty($element['#default_value']) && $element['#value'] === $element['#default_value']) {
        $element['#value'] = static::formatTime($element['#attributes']['data-webform-time-format'], strtotime($element['#value']));
      }
    }
    else {
      $element['#attributes']['type'] = 'time';
    }
    Element::setAttributes($element, ['id', 'name', 'type', 'value', 'size', 'maxlength', 'min', 'max', 'step']);
    static::setAttributes($element, ['form-time', 'webform-time']);
    return $element;
  }

  /**
   * Format custom time.
   *
   * @param string $custom_format
   *   A PHP date format string suitable for input to date().
   * @param int $timestamp
   *   (optional) A UNIX timestamp to format.
   *
   * @return string
   *   Formatted time.
   */
  protected static function formatTime($custom_format, $timestamp = NULL) {
    /** @var \Drupal\Core\Datetime\DateFormatterInterface $date_formatter */
    $date_formatter = \Drupal::service('date.formatter');
    return $date_formatter->format($timestamp ?: time(), 'custom', $custom_format);
  }

}
