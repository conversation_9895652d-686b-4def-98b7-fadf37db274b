uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_location_geocomplete_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_element_loc_geocomplete
title: 'Test: Element: Location Geocomplete'
description: 'Test (geo)location elements with Geocomplete.'
categories:
  - 'Test: Element'
elements: |
  location_default_element:
    '#type': details
    '#title': 'Location default element'
    '#open': true
    location_default:
      '#type': webform_location_geocomplete
      '#title': 'location default'
    location_map:
      '#type': webform_location_geocomplete
      '#title': 'location map'
      '#geolocation': true
      '#map': true
  location_attributes_element:
    '#type': details
    '#title': 'Location with visible attributes element'
    '#open': true
    location_attributes:
      '#type': webform_location_geocomplete
      '#title': 'Location with visible attributes'
      '#lat__access': true
      '#lng__access': true
      '#location__access': true
      '#formatted_address__access': true
      '#street_address__access': true
      '#street_number__access': true
      '#postal_code__access': true
      '#locality__access': true
      '#sublocality__access': true
      '#subpremise__access': true
      '#administrative_area_level_1__access': true
      '#country__access': true
      '#country_short__access': true
  location_default_value_required_element:
    '#type': details
    '#title': 'Location with default value and required element'
    '#open': true
    location_default_value_required:
      '#type': webform_location_geocomplete
      '#title': 'Location with default value and required'
      '#default_value':
        value: '1600 Pennsylvania Ave NW, Washington, DC 20500'
      '#required': true
      '#lat__access': true
      '#lng__access': true
      '#location__access': true
      '#formatted_address__access': true
      '#street_address__access': true
      '#street_number__access': true
      '#postal_code__access': true
      '#locality__access': true
      '#sublocality__access': true
      '#subpremise__access': true
      '#administrative_area_level_1__access': true
      '#country__access': true
      '#country_short__access': true
  location_geolocation_element:
    '#type': details
    '#title': 'Location with geolocation element'
    '#open': true
    location_geolocation:
      '#type': webform_location_geocomplete
      '#title': 'Location with geolocation'
      '#geolocation': true
      '#location__access': true
      '#formatted_address__access': true
      '#street_address__access': true
      '#street_number__access': true
      '#postal_code__access': true
      '#locality__access': true
      '#sublocality__access': true
      '#subpremise__access': true
      '#administrative_area_level_1__access': true
      '#country__access': true
      '#country_short__access': true
  location_geolocation_hidden:
    '#type': webform_location_geocomplete
    '#title': 'Location with geolocation and hidden'
    '#geolocation': true
    '#hidden': true
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  debug:
    id: debug
    label: Debug
    notes: ''
    handler_id: debug
    status: true
    conditions: {  }
    weight: 0
    settings: {  }
variants: {  }
