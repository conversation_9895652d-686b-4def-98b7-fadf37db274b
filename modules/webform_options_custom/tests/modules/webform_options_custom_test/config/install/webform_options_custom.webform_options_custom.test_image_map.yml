langcode: en
status: true
dependencies: {  }
id: test_image_map
label: 'Test: Image map'
description: 'An image map'
help: 'An image map'
category: Test
type: template
template: |
  <img src="https://interactive-examples.mdn.mozilla.net/media/examples/mdn-info2.png" width="260" height="232" alt="MDN Infographic" usemap="#infographic">

  <map name="infographic">
    <area shape="poly" coords="130,147,200,107,254,219,130,228" href="https://developer.mozilla.org/docs/Web/HTML" target="_blank" data-id="html"  alt="HTML" />
    <area shape="poly" coords="130,147,130,228,6,219,59,107" href="https://developer.mozilla.org/docs/Web/CSS" target="_blank" data-id="css" alt="CSS" />
    <area shape="poly" coords="130,147,200,107,130,4,59,107" href="https://developer.mozilla.org/docs/Web/JavaScript" target="_blank" data-id="javascript" alt="JavaScript" />
  </map>
url: ''
css: ''
javascript: ''
options: '{  }'
value_attributes: data-id
text_attributes: alt
fill: false
zoom: false
tooltip: false
show_select: true
element: true
entity_reference: false
