langcode: en
status: true
dependencies: {  }
id: test_html
label: 'Test: HTML'
description: 'Basic HTML markup'
help: 'Basic HTML markup'
category: Test
type: template
template: |
  <style>
  .webform-options-custom--test-html [data-option-value] {
    display: inline-block;
    width: 80px;
    border: 3px solid #000;
    padding: 5px 20px;
    margin: 0 10px 0 0;
    text-align: center;
  }
  </style>
  <div data-id="one" data-name="One">One</div>
  <div data-id="two" data-name="Two">Two</div>
  <div data-id="three" data-name="Three">Three</div>
url: ''
css: ''
javascript: ''
options: ''
value_attributes: data-id
text_attributes: data-name
fill: true
zoom: false
tooltip: true
show_select: true
element: true
entity_reference: false
