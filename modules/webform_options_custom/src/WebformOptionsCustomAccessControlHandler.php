<?php

namespace Drupal\webform_options_custom;

use <PERSON>upal\Core\Access\AccessResult;
use Drupal\Core\Entity\EntityAccessControlHandler;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Session\AccountInterface;

/**
 * Defines the access control handler for the webform options custom entity type.
 *
 * @see \Drupal\webform_options_custom\Entity\WebformOptionsCustom.
 */
class WebformOptionsCustomAccessControlHandler extends EntityAccessControlHandler {

  /**
   * {@inheritdoc}
   */
  public function checkAccess(EntityInterface $entity, $operation, AccountInterface $account) {
    return AccessResult::allowedIfHasPermission($account, 'administer webform');
  }

}
