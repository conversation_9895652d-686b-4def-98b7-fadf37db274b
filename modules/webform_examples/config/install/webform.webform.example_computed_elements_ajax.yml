uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_examples
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: example_computed_elements_ajax
title: 'Example: Computed Ajax'
description: 'Example of a computed element using Ajax.'
categories:
  - Example
elements: |
  message_warning:
    '#type': webform_message
    '#states':
      visible:
        ':input[name="c"]':
          value: c
    '#message_type': warning
    '#message_message': 'Please enter <strong>a</strong> and <strong>b</strong> to perform a basic adding calculation.'
  message_status:
    '#type': webform_message
    '#message_message': 'Thank you for entering <strong>a</strong> and <strong>b</strong> to perform a basic adding calculation.'
    '#states':
      visible:
        ':input[name="c"]':
          '!value': c
  calculation:
    '#type': fieldset
    '#title': 'Basic adding calculation'
    container:
      '#type': container
      '#attributes':
        class:
          - container-inline
          - calculation
      a:
        '#type': number
        '#title': a
        '#title_display': invisible
        '#placeholder': a
        '#required': true
      markup_add:
        '#type': webform_markup
        '#markup': ' + '
      b:
        '#type': number
        '#title': b
        '#title_display': invisible
        '#placeholder': b
        '#required': true
      markup_equals:
        '#type': webform_markup
        '#markup': ' = '
      c:
        '#type': webform_computed_twig
        '#title': c
        '#title_display': invisible
        '#ajax': true
        '#template': '{% if data.a|length and data.b|length %}{{ data.a + data.b }}{% else %}c{% endif %}'
  user_summary:
    '#type': fieldset
    '#title': 'User information'
    user:
      '#type': webform_entity_select
      '#title': 'User: Summary'
      '#target_type': user
      '#selection_handler': 'default:user'
      '#selection_settings':
        include_anonymous: false
        filter:
          type: _none
    user_preview:
      '#type': webform_computed_token
      '#title': User
      '#title_display': none
      '#ajax': true
      '#hide_empty': true
      '#template': |
        <strong>User: ID:</strong> [webform_submission:values:user:entity:uid:clear]<br/>
        <strong>User: Display name:</strong> [webform_submission:values:user:entity:display-name:clear]<br/>
        <strong>User: Email:</strong> [webform_submission:values:user:entity:mail:clear]<br/>
        <strong>User: URL:</strong> [webform_submission:values:user:entity:url:clear]<br/>
        <strong>User: Created:</strong> [webform_submission:values:user:entity:created:clear]
        
      '#display_on': form
    user_uid:
      '#type': webform_computed_token
      '#title': 'User: ID'
      '#template': '[webform_submission:values:user:entity:uid:clear]'
      '#display_on': view
    user_display_name:
      '#type': webform_computed_token
      '#title': 'User: Display name'
      '#template': '[webform_submission:values:user:entity:display-name:clear]'
      '#display_on': view
    user_mail:
      '#type': webform_computed_token
      '#title': 'User: Email'
      '#template': '[webform_submission:values:user:entity:mail:clear]'
      '#display_on': view
    user_url:
      '#type': webform_computed_token
      '#title': 'User: URL'
      '#template': '[webform_submission:values:user:entity:url:clear]'
      '#display_on': view
    user_created:
      '#type': webform_computed_token
      '#title': 'User: Created'
      '#template': '[webform_submission:values:user:entity:created:clear]'
      '#display_on': view
  bmi_calculator:
    '#type': fieldset
    '#title': 'Body Mass Index (BMI) calculator'
    '#description': |
      Formula: weight (lb) / [height (in)]2 x 703<br/>
      @see <a href="https://www.cdc.gov/nccdphp/dnpao/growthcharts/training/bmiage/page5_2.html">https://www.cdc.gov/nccdphp/dnpao/growthcharts/training/bmiage/page5_2.html</a>
      
    weight:
      '#type': number
      '#title': Weight
      '#field_suffix': ' pounds'
      '#default_value': 37.25
      '#step': 0.25
      '#min': 1
    height:
      '#type': webform_height
      '#title': Height
      '#default_value': 41.5
      '#inches__step': 0.5
    bmi:
      '#type': webform_computed_twig
      '#title': BMI
      '#ajax': true
      '#template': '{% if data.weight|length and data.height|length %}{{ (data.weight / (data.height * data.height) * 703)|number_format(2) }}{% endif %}'
css: |
  .calculation {
    font-size: 2em;
  }
  .calculation input {
    width: 4em;
    text-align: right;
  }
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: true
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
