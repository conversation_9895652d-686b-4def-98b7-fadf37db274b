<div class="page-header">
  <h2><a href="http://getbootstrap.com/css/#tables">Tables</a></h2>
</div>

<table class="table table-striped table-bordered table-hover table-condensed table-responsive">
  <caption>table.table-striped.table-bordered.table-hover.table-condensed.table-responsive</caption>
  <thead>
  <tr>
    <th>#</th>
    <th>First Name</th>
    <th>Last Name</th>
    <th>Username</th>
  </tr>
  </thead>
  <tbody>
  <tr>
    <th scope="row">1</th>
    <td>Mark</td>
    <td>Otto</td>
    <td>@mdo</td>
  </tr>
  <tr>
    <th scope="row">2</th>
    <td>Jacob</td>
    <td>Thornton</td>
    <td>@fat</td>
  </tr>
  <tr>
    <th scope="row">3</th>
    <td>Larry</td>
    <td>the Bird</td>
    <td>@twitter</td>
  </tr>
  </tbody>
</table>

<table class="table">
  <caption>Contextual classes</caption>
  <tbody>
  <tr class="active">
    <th scope="row">.active</th>
    <td>Applies the hover color to a particular row or cell</td>
  </tr>
  <tr class="success">
    <th scope="row">.success</th>
    <td>Indicates a successful or positive action</td>
  </tr>
  <tr class="info">
    <th scope="row">.info</th>
    <td>Indicates a neutral informative change or action</td>
  </tr>
  <tr class="warning">
    <th scope="row">.warning</th>
    <td>Indicates a warning that might need attention</td>
  </tr>
  <tr class="danger">
    <th scope="row">.danger</th>
    <td>Indicates a dangerous or potentially negative action</td>
  </tr>
  </tbody>
</table>

