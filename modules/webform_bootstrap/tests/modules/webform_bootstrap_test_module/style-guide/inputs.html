<div class="page-header">
  <h2><a href="http://getbootstrap.com/css/#inputs">Inputs</a></h2>
</div>

<form class="form-horizontal">
  <div class="form-group">
    <label class="col-sm-2 control-label">Text input</label>
    <div class="col-sm-10">
      <input type="text" class="form-control" placeholder="Text input">
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label">Textarea</label>
    <div class="col-sm-10">
      <textarea class="form-control" rows="3"></textarea>
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label">Select</label>
    <div class="col-sm-10">
      <select class="form-control">
        <option>1</option>
        <option>2</option>
        <option>3</option>
        <option>4</option>
        <option>5</option>
      </select>
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label">Checkboxes</label>
    <div class="col-sm-10">
      <div class="checkbox">
        <label><input type="checkbox" value="">checkbox</label>
      </div>
      <div class="checkbox disabled">
        <label><input type="checkbox" value="" disabled>checkbox disabled</label>
      </div>
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label">Checkboxes (inline)</label>
    <div class="checkbox">
      <label class="checkbox-inline"><input type="checkbox" value="option1">checkbox.inline</label>
      <label class="checkbox-inline"><input type="checkbox" value="option1">checkbox.inline</label>
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label">Radios</label>
    <div class="col-sm-10">
      <div class="radio">
        <label><input type="radio" name="optionsRadios" value="option1" checked>radio</label>
      </div>
      <div class="radio">
        <label><input type="radio" name="optionsRadios" value="option2">radio</label>
      </div>
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label">Buttons</label>
    <div class="col-sm-10">
      <a class="btn btn-default" href="#" role="button">Link</a>
      <button class="btn btn-default" type="submit">Button</button>
      <input class="btn btn-default" type="button" value="Input">
      <input class="btn btn-default" type="submit" value="Submit">
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label">Button colors</label>
    <div class="col-sm-10">
      <button type="button" class="btn btn-default">Default</button>
      <button type="button" class="btn btn-primary">Primary</button>
      <button type="button" class="btn btn-success">Success</button>
      <button type="button" class="btn btn-info">Info</button>
      <button type="button" class="btn btn-warning">Warning</button>
      <button type="button" class="btn btn-danger">Danger</button>
      <button type="button" class="btn btn-link">Link</button>
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label">Button sizes</label>
    <div class="col-sm-10">
      <a href="#" class="btn btn-primary btn-lg">Large button</a>
      <a href="#" class="btn btn-primary">Default button</a>
      <a href="#" class="btn btn-primary btn-sm">Small button</a>
      <a href="#" class="btn btn-primary btn-xs">Mini button</a>
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label">Dropdown button</label>
    <div class="col-sm-10">
      <div class="btn-group">
        <a href="#" class="btn btn-default">Default</a>
        <a href="#" class="btn btn-default dropdown-toggle" data-toggle="dropdown"><span class="caret"></span></a>
        <ul class="dropdown-menu">
          <li><a href="#">Action</a></li>
          <li><a href="#">Another action</a></li>
          <li><a href="#">Something else here</a></li>
          <li class="divider"></li>
          <li><a href="#">Separated link</a></li>
        </ul>
      </div>
    </div>
  </div>
  <div class="form-group">
    <label class="col-sm-2 control-label">Inputs states</label>
    <div class="col-sm-10">
      <div class="form-group">
        <label class="control-label" for="focusedInput">Focused input</label>
        <input class="form-control" id="focusedInput" type="text" value="This is focused…">
      </div>

      <div class="form-group">
        <label class="control-label" for="disabledInput">Disabled input</label>
        <input class="form-control" id="disabledInput" type="text" placeholder="Disabled input here…" disabled="">
      </div>

      <div class="form-group has-warning">
        <label class="control-label" for="inputWarning">Input warning</label>
        <input type="text" class="form-control" id="inputWarning">
      </div>

      <div class="form-group has-error">
        <label class="control-label" for="inputError">Input error</label>
        <input type="text" class="form-control" id="inputError">
      </div>

      <div class="form-group has-success">
        <label class="control-label" for="inputSuccess">Input success</label>
        <input type="text" class="form-control" id="inputSuccess">
      </div>

      <div class="form-group">
        <label class="control-label" for="inputLarge">Large input</label>
        <input class="form-control input-lg" type="text" id="inputLarge">
      </div>

      <div class="form-group">
        <label class="control-label" for="inputDefault">Default input</label>
        <input type="text" class="form-control" id="inputDefault">
      </div>

      <div class="form-group">
        <label class="control-label" for="inputSmall">Small input</label>
        <input class="form-control input-sm" type="text" id="inputSmall">
      </div>

      <div class="form-group">
        <label class="control-label">Input addons</label>
        <div class="input-group">
          <span class="input-group-addon">$</span>
          <input type="text" class="form-control">
          <span class="input-group-btn">
            <button class="btn btn-default" type="button">Button</button>
          </span>
        </div>
      </div>
    </div>
  </div>
</form>
