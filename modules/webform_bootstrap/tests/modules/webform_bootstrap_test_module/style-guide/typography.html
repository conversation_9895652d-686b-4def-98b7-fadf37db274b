<div class="page-header">
  <h2><a href="http://getbootstrap.com/css/#type">Typography</a></h2>
</div>

<h3>Headings</h3>

<div class="page-header">
  <h1>Page Header</h1>
</div>

<h1>h1. Header 1 <small>Secondary text</small></h1>
<h2>h2. Header 2 <small>Secondary text</small></h2>
<h3>h3. Header 3 <small>Secondary text</small></h3>
<h4>h4. Header 4 <small>Secondary text</small></h4>
<h5>h5. Header 5 <small>Secondary text</small></h5>
<h6>h6. Header 6 <small>Secondary text</small></h6>

<h3>Block elements</h3>

<p><b>p(aragraph)</b>: Maecenas sed diam eget risus varius blandit sit amet non magna. Donec id elit non mi porta gravida at eget metus. Duis mollis, est non commodo luctus, nisi erat porttitor ligula, eget lacinia odio sem nec elit.</p>

<p class="lead"><b>p(aragraph).lead</b>: <PERSON>cenas sed diam eget risus varius blandit sit amet non magna. Donec id elit non mi porta gravida at eget metus. Duis mollis, est non commodo luctus, nisi erat porttitor ligula, eget lacinia odio sem nec elit.</p>

<address><b>address:</b> <strong>Twitter, Inc.</strong><br>1355 Market Street, Suite 900<br>San Francisco, CA 94103<br><abbr title="Phone">P:</abbr> (*************</address>

<blockquote>
  <p><b>blockquote:</b> Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante.</p>
  <footer>footer <cite title="Citation Title">cite</cite></footer>
</blockquote>

<blockquote class="blockquote-reverse">
  <p><b>blockquote.blockquote-reverse:</b> Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante.</p>
  <footer>footer <cite title="Citation Title">cite</cite></footer>
</blockquote>

<pre><b>pre(formatted)</b>&lt;p&gt;Sample text here…&lt;/p&gt;</pre>

<pre class=".pre-scrollable"><b>pre.pre-scrollable</b>&lt;p&gt;Sample text here…&lt;/p&gt;</pre>

<h3>Alignment classes</h3>

<p class="text-left">Left aligned text.</p>
<p class="text-center">Center aligned text.</p>
<p class="text-right">Right aligned text.</p>
<p class="text-justify">Justified text.</p>
<p class="text-nowrap">No wrap text.</p>

<h3>Lists</h3>

<p><b>Ordered list</b></p>
<ol>
  <li>Lorem ipsum dolor sit amet</li>
  <li>Consectetur adipiscing elit</li>
  <li>Integer molestie lorem at massa</li>
  <li>Facilisis in pretium nisl aliquet</li>
  <li>Nulla volutpat aliquam velit</li>
  <li>Faucibus porta lacus fringilla vel</li>
  <li>Aenean sit amet erat nunc</li>
  <li>Eget porttitor lorem</li>
</ol>

<p><b>Unordered list</b></p>
<ul>
  <li>Lorem ipsum dolor sit amet</li>
  <li>Consectetur adipiscing elit</li>
  <li>Integer molestie lorem at massa</li>
  <li>Facilisis in pretium nisl aliquet</li>
  <li>Nulla volutpat aliquam velit
    <ul>
      <li>Phasellus iaculis neque</li>
      <li>Purus sodales ultricies</li>
      <li>Vestibulum laoreet porttitor sem</li>
      <li>Ac tristique libero volutpat at</li>
    </ul>
  </li>
  <li>Faucibus porta lacus fringilla vel</li>
  <li>Aenean sit amet erat nunc</li>
  <li>Eget porttitor lorem</li>
</ul>

<p><b>List inline</b></p>

<ul class="list-inline">
  <li>Lorem ipsum</li>
  <li>Phasellus iaculis</li>
  <li>Nulla volutpat</li>
</ul>

<p><b>Definition list</b></p>

<dl>
  <dt>Description lists</dt>
  <dd>A description list is perfect for defining terms.</dd>
  <dt>Euismod</dt>
  <dd>Vestibulum id ligula porta felis euismod semper eget lacinia odio sem nec elit.</dd>
  <dd>Donec id elit non mi porta gravida at eget metus.</dd>
  <dt>Malesuada porta</dt>
  <dd>Etiam porta sem malesuada magna mollis euismod.</dd>
</dl>

<p><b>Definition list horizontal</b></p>

<dl class="dl-horizontal">
  <dt>Description lists</dt>
  <dd>A description list is perfect for defining terms.</dd>
  <dt>Euismod</dt>
  <dd>Vestibulum id ligula porta felis euismod semper eget lacinia odio sem nec elit.</dd>
  <dd>Donec id elit non mi porta gravida at eget metus.</dd>
  <dt>Malesuada porta</dt>
  <dd>Etiam porta sem malesuada magna mollis euismod.</dd>
  <dt>Felis euismod semper eget lacinia</dt>
  <dd>Fusce dapibus, tellus ac cursus commodo, tortor mauris condimentum nibh, ut fermentum massa justo sit amet risus.</dd>
</dl>

<h3>Inline text elements</h3>

<p>
  <sup>sup</sup>(erscript)
  <br />
  <sub>sub</sub>(script)
  <br />
  <mark>mark</mark>(ed text)
  <br />
  <del>del</del>(ete)
  <br />
  <s>s</s>(trikethough)
  <br />
  <ins>ins</ins>(serted)
  <br />
  <u>un</u>(derline)
  <br />
  <small>small</small>
  <br />
  <b>b</b>(old)
  <br />
  <em>em</em>(phasis)
  <br />
  <code>&lt;section&gt;</code>
  <br />
  <kbd>kdb</kbd>(keyboard)
  <br />
  <var>var</var>(iable)
  <br />
  <samp>samp</samp>(le)
  <br />
  <abbr title="attribute">attr</abbr>(ibute)
  <br />
  <abbr title="HyperText Markup Language" class="initialism">attr.initialism</abbr>
  <br />

</p>

<h3>Transformation classes</h3>

<p>
  <span class="text-lowercase">Lowercased text.</span>
  <br />
  <span class="text-uppercase">Uppercased text.</span>
  <br />
  <span class="text-capitalize">Capitalized text.</span>
  <br />
</p>

<h3>Contextual colors</h3>

<p>
  <span class="text-muted">.text-muted</span>
  <span class="text-primary">.text-primary</span>
  <span class="text-success">.text-success</span>
  <span class="text-info">.text-info</span>
  <span class="text-warning">.text-warning</span>
  <span class="text-danger">.text-danger</span>
</p>

<h3>Contextual backgrounds</h3>

<p>
  <span class="bg-muted">.bg-muted</span>
  <span class="bg-primary">.bg-primary</span>
  <span class="bg-success">.bg-success</span>
  <span class="bg-info">.bg-info</span>
  <span class="bg-warning">.bg-warning</span>
  <span class="bg-danger">.bg-danger</span>
</p>

<h3>Labels</h3>

<p>
  <span class="label label-default">label-default</span>
  <span class="label label-primary">label-primary</span>
  <span class="label label-success">label-success</span>
  <span class="label label-warning">label-warning</span>
  <span class="label label-danger">label-danger</span>
  <span class="label label-info">label-info</span>
</p>

<h3><a href="http://getbootstrap.com/components/#alerts">Alerts</a></h3>

<div class="alert alert-dismissible alert-warning">
  <button type="button" class="close" data-dismiss="alert">×</button>
  <strong>Warning</strong> <a href="#" class="alert-link">link</a> and some text.
</div>

<div class="alert alert-dismissible alert-danger">
  <button type="button" class="close" data-dismiss="alert">×</button>
  <strong>Danger</strong> <a href="#" class="alert-link">link</a> and some text.
</div>

<div class="alert alert-dismissible alert-success">
  <button type="button" class="close" data-dismiss="alert">×</button>
  <strong>Success</strong> <a href="#" class="alert-link">link</a> and some text.
</div>

<div class="alert alert-dismissible alert-info">
  <button type="button" class="close" data-dismiss="alert">×</button>
  <strong>Info</strong> <a href="#" class="alert-link">link</a> and some text.
</div>

<h3>Wells</h3>

<div class="well"><b>div.well</b> Look, I'm in a well!</div>
<div class="well well-sm"><b>div.well.well-sm</b> Look, I'm in a small well!</div>
<div class="well well-lg"><b>div.well.well-lg</b> Look, I'm in a large well!</div>
