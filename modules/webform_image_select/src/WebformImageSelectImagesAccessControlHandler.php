<?php

namespace Drupal\webform_image_select;

use <PERSON><PERSON>al\Core\Access\AccessResult;
use Drupal\Core\Entity\EntityAccessControlHandler;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Session\AccountInterface;

/**
 * Defines the access control handler for the webform image select images entity type.
 *
 * @see \Drupal\webform_image_select\Entity\WebformImageSelectImages.
 */
class WebformImageSelectImagesAccessControlHandler extends EntityAccessControlHandler {

  /**
   * {@inheritdoc}
   */
  public function checkAccess(EntityInterface $entity, $operation, AccountInterface $account) {
    return AccessResult::allowedIfHasPermission($account, 'administer webform');
  }

}
