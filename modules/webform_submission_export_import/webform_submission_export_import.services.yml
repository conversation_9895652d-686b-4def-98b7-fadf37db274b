services:
  webform_submission_export_import.importer:
    class: <PERSON><PERSON>al\webform_submission_export_import\WebformSubmissionExportImportImporter
    arguments: ['@config.factory', '@logger.factory', '@entity_type.manager', '@plugin.manager.webform.element', '@file_system']

  webform_submission_export_import.route_subscriber:
    class: <PERSON>upal\webform_submission_export_import\Routing\WebformSubmissionExportImportRouteSubscriber
    arguments: ['@module_handler']
    tags:
      - { name: event_subscriber }

  # Logger.

  logger.channel.webform_submission_export_import:
    class: Drupal\Core\Logger\LoggerChannel
    factory: logger.factory:get
    arguments: ['webform_submission_export_import']
