{#
/**
 * @file
 * Default theme implementation for a webform section element and its children.
 *
 * Available variables:
 * - attributes: HTML attributes for the <section> element.
 * - title: The title/header of the section header.
 * - title_attributes: HTML attributes to apply to the title/header element.
 * - description: The description element containing the following properties:
 *   - content: The description content of the <div>.
 *   - attributes: HTML attributes to apply to the description container.
 * - children: The rendered child elements of the <div>.
 * - prefix: The content to add before the .section-wrapper children.
 * - suffix: The content to add after the .section-wrapper children.
 *
 * Copied from: fieldset.html.twig
 *
 * @see template_preprocess_webform_card()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    'js-form-wrapper',
    'form-wrapper',
    'webform-card',
  ]
%}
<section{{ attributes.addClass(classes) }}>
  {%
    set title_classes = [
      'webform-card-title',
    ]
  %}
  {% if title and title_display != 'none' %}
    <{{ title_tag }}{{ title_attributes.addClass(title_classes) }}>{{ title }}</{{ title_tag }}>
  {% endif %}
  <div class="webform-card-wrapper">
    {% if description.content %}
      <div{{ description.attributes.addClass('description') }}>{{ description.content }}</div>
    {% endif %}
    {% if prefix %}
      <span class="field-prefix">{{ prefix }}</span>
    {% endif %}
    {{ children }}
    {% if suffix %}
      <span class="field-suffix">{{ suffix }}</span>
    {% endif %}
  </div>
</section>
