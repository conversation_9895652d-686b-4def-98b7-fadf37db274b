uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_cards_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_cards_long_100
title: 'Test: Webform: Cards: Long - 100 cards'
description: 'Test form with 100 cards'
categories:
  - 'Test: Cards'
elements: |
  card_1:
    '#type': webform_card
    '#title': 'Card #1'
    element_1:
      '#type': textfield
      '#title': 'Element #1'
  card_2:
    '#type': webform_card
    '#title': 'Card #2'
    element_2:
      '#type': textfield
      '#title': 'Element #2'
  card_3:
    '#type': webform_card
    '#title': 'Card #3'
    element_3:
      '#type': textfield
      '#title': 'Element #3'
  card_4:
    '#type': webform_card
    '#title': 'Card #4'
    element_4:
      '#type': textfield
      '#title': 'Element #4'
  card_5:
    '#type': webform_card
    '#title': 'Card #5'
    element_5:
      '#type': textfield
      '#title': 'Element #5'
  card_6:
    '#type': webform_card
    '#title': 'Card #6'
    element_6:
      '#type': textfield
      '#title': 'Element #6'
  card_7:
    '#type': webform_card
    '#title': 'Card #7'
    element_7:
      '#type': textfield
      '#title': 'Element #7'
  card_8:
    '#type': webform_card
    '#title': 'Card #8'
    element_8:
      '#type': textfield
      '#title': 'Element #8'
  card_9:
    '#type': webform_card
    '#title': 'Card #9'
    element_9:
      '#type': textfield
      '#title': 'Element #9'
  card_10:
    '#type': webform_card
    '#title': 'Card #10'
    element_10:
      '#type': textfield
      '#title': 'Element #10'
  card_11:
    '#type': webform_card
    '#title': 'Card #11'
    element_11:
      '#type': textfield
      '#title': 'Element #11'
  card_12:
    '#type': webform_card
    '#title': 'Card #12'
    element_12:
      '#type': textfield
      '#title': 'Element #12'
  card_13:
    '#type': webform_card
    '#title': 'Card #13'
    element_13:
      '#type': textfield
      '#title': 'Element #13'
  card_14:
    '#type': webform_card
    '#title': 'Card #14'
    element_14:
      '#type': textfield
      '#title': 'Element #14'
  card_15:
    '#type': webform_card
    '#title': 'Card #15'
    element_15:
      '#type': textfield
      '#title': 'Element #15'
  card_16:
    '#type': webform_card
    '#title': 'Card #16'
    element_16:
      '#type': textfield
      '#title': 'Element #16'
  card_17:
    '#type': webform_card
    '#title': 'Card #17'
    element_17:
      '#type': textfield
      '#title': 'Element #17'
  card_18:
    '#type': webform_card
    '#title': 'Card #18'
    element_18:
      '#type': textfield
      '#title': 'Element #18'
  card_19:
    '#type': webform_card
    '#title': 'Card #19'
    element_19:
      '#type': textfield
      '#title': 'Element #19'
  card_20:
    '#type': webform_card
    '#title': 'Card #20'
    element_20:
      '#type': textfield
      '#title': 'Element #20'
  card_21:
    '#type': webform_card
    '#title': 'Card #21'
    element_21:
      '#type': textfield
      '#title': 'Element #21'
  card_22:
    '#type': webform_card
    '#title': 'Card #22'
    element_22:
      '#type': textfield
      '#title': 'Element #22'
  card_23:
    '#type': webform_card
    '#title': 'Card #23'
    element_23:
      '#type': textfield
      '#title': 'Element #23'
  card_24:
    '#type': webform_card
    '#title': 'Card #24'
    element_24:
      '#type': textfield
      '#title': 'Element #24'
  card_25:
    '#type': webform_card
    '#title': 'Card #25'
    element_25:
      '#type': textfield
      '#title': 'Element #25'
  card_26:
    '#type': webform_card
    '#title': 'Card #26'
    element_26:
      '#type': textfield
      '#title': 'Element #26'
  card_27:
    '#type': webform_card
    '#title': 'Card #27'
    element_27:
      '#type': textfield
      '#title': 'Element #27'
  card_28:
    '#type': webform_card
    '#title': 'Card #28'
    element_28:
      '#type': textfield
      '#title': 'Element #28'
  card_29:
    '#type': webform_card
    '#title': 'Card #29'
    element_29:
      '#type': textfield
      '#title': 'Element #29'
  card_30:
    '#type': webform_card
    '#title': 'Card #30'
    element_30:
      '#type': textfield
      '#title': 'Element #30'
  card_31:
    '#type': webform_card
    '#title': 'Card #31'
    element_31:
      '#type': textfield
      '#title': 'Element #31'
  card_32:
    '#type': webform_card
    '#title': 'Card #32'
    element_32:
      '#type': textfield
      '#title': 'Element #32'
  card_33:
    '#type': webform_card
    '#title': 'Card #33'
    element_33:
      '#type': textfield
      '#title': 'Element #33'
  card_34:
    '#type': webform_card
    '#title': 'Card #34'
    element_34:
      '#type': textfield
      '#title': 'Element #34'
  card_35:
    '#type': webform_card
    '#title': 'Card #35'
    element_35:
      '#type': textfield
      '#title': 'Element #35'
  card_36:
    '#type': webform_card
    '#title': 'Card #36'
    element_36:
      '#type': textfield
      '#title': 'Element #36'
  card_37:
    '#type': webform_card
    '#title': 'Card #37'
    element_37:
      '#type': textfield
      '#title': 'Element #37'
  card_38:
    '#type': webform_card
    '#title': 'Card #38'
    element_38:
      '#type': textfield
      '#title': 'Element #38'
  card_39:
    '#type': webform_card
    '#title': 'Card #39'
    element_39:
      '#type': textfield
      '#title': 'Element #39'
  card_40:
    '#type': webform_card
    '#title': 'Card #40'
    element_40:
      '#type': textfield
      '#title': 'Element #40'
  card_41:
    '#type': webform_card
    '#title': 'Card #41'
    element_41:
      '#type': textfield
      '#title': 'Element #41'
  card_42:
    '#type': webform_card
    '#title': 'Card #42'
    element_42:
      '#type': textfield
      '#title': 'Element #42'
  card_43:
    '#type': webform_card
    '#title': 'Card #43'
    element_43:
      '#type': textfield
      '#title': 'Element #43'
  card_44:
    '#type': webform_card
    '#title': 'Card #44'
    element_44:
      '#type': textfield
      '#title': 'Element #44'
  card_45:
    '#type': webform_card
    '#title': 'Card #45'
    element_45:
      '#type': textfield
      '#title': 'Element #45'
  card_46:
    '#type': webform_card
    '#title': 'Card #46'
    element_46:
      '#type': textfield
      '#title': 'Element #46'
  card_47:
    '#type': webform_card
    '#title': 'Card #47'
    element_47:
      '#type': textfield
      '#title': 'Element #47'
  card_48:
    '#type': webform_card
    '#title': 'Card #48'
    element_48:
      '#type': textfield
      '#title': 'Element #48'
  card_49:
    '#type': webform_card
    '#title': 'Card #49'
    element_49:
      '#type': textfield
      '#title': 'Element #49'
  card_50:
    '#type': webform_card
    '#title': 'Card #50'
    element_50:
      '#type': textfield
      '#title': 'Element #50'
  card_51:
    '#type': webform_card
    '#title': 'Card #51'
    element_51:
      '#type': textfield
      '#title': 'Element #51'
  card_52:
    '#type': webform_card
    '#title': 'Card #52'
    element_52:
      '#type': textfield
      '#title': 'Element #52'
  card_53:
    '#type': webform_card
    '#title': 'Card #53'
    element_53:
      '#type': textfield
      '#title': 'Element #53'
  card_54:
    '#type': webform_card
    '#title': 'Card #54'
    element_54:
      '#type': textfield
      '#title': 'Element #54'
  card_55:
    '#type': webform_card
    '#title': 'Card #55'
    element_55:
      '#type': textfield
      '#title': 'Element #55'
  card_56:
    '#type': webform_card
    '#title': 'Card #56'
    element_56:
      '#type': textfield
      '#title': 'Element #56'
  card_57:
    '#type': webform_card
    '#title': 'Card #57'
    element_57:
      '#type': textfield
      '#title': 'Element #57'
  card_58:
    '#type': webform_card
    '#title': 'Card #58'
    element_58:
      '#type': textfield
      '#title': 'Element #58'
  card_59:
    '#type': webform_card
    '#title': 'Card #59'
    element_59:
      '#type': textfield
      '#title': 'Element #59'
  card_60:
    '#type': webform_card
    '#title': 'Card #60'
    element_60:
      '#type': textfield
      '#title': 'Element #60'
  card_61:
    '#type': webform_card
    '#title': 'Card #61'
    element_61:
      '#type': textfield
      '#title': 'Element #61'
  card_62:
    '#type': webform_card
    '#title': 'Card #62'
    element_62:
      '#type': textfield
      '#title': 'Element #62'
  card_63:
    '#type': webform_card
    '#title': 'Card #63'
    element_63:
      '#type': textfield
      '#title': 'Element #63'
  card_64:
    '#type': webform_card
    '#title': 'Card #64'
    element_64:
      '#type': textfield
      '#title': 'Element #64'
  card_65:
    '#type': webform_card
    '#title': 'Card #65'
    element_65:
      '#type': textfield
      '#title': 'Element #65'
  card_66:
    '#type': webform_card
    '#title': 'Card #66'
    element_66:
      '#type': textfield
      '#title': 'Element #66'
  card_67:
    '#type': webform_card
    '#title': 'Card #67'
    element_67:
      '#type': textfield
      '#title': 'Element #67'
  card_68:
    '#type': webform_card
    '#title': 'Card #68'
    element_68:
      '#type': textfield
      '#title': 'Element #68'
  card_69:
    '#type': webform_card
    '#title': 'Card #69'
    element_69:
      '#type': textfield
      '#title': 'Element #69'
  card_70:
    '#type': webform_card
    '#title': 'Card #70'
    element_70:
      '#type': textfield
      '#title': 'Element #70'
  card_71:
    '#type': webform_card
    '#title': 'Card #71'
    element_71:
      '#type': textfield
      '#title': 'Element #71'
  card_72:
    '#type': webform_card
    '#title': 'Card #72'
    element_72:
      '#type': textfield
      '#title': 'Element #72'
  card_73:
    '#type': webform_card
    '#title': 'Card #73'
    element_73:
      '#type': textfield
      '#title': 'Element #73'
  card_74:
    '#type': webform_card
    '#title': 'Card #74'
    element_74:
      '#type': textfield
      '#title': 'Element #74'
  card_75:
    '#type': webform_card
    '#title': 'Card #75'
    element_75:
      '#type': textfield
      '#title': 'Element #75'
  card_76:
    '#type': webform_card
    '#title': 'Card #76'
    element_76:
      '#type': textfield
      '#title': 'Element #76'
  card_77:
    '#type': webform_card
    '#title': 'Card #77'
    element_77:
      '#type': textfield
      '#title': 'Element #77'
  card_78:
    '#type': webform_card
    '#title': 'Card #78'
    element_78:
      '#type': textfield
      '#title': 'Element #78'
  card_79:
    '#type': webform_card
    '#title': 'Card #79'
    element_79:
      '#type': textfield
      '#title': 'Element #79'
  card_80:
    '#type': webform_card
    '#title': 'Card #80'
    element_80:
      '#type': textfield
      '#title': 'Element #80'
  card_81:
    '#type': webform_card
    '#title': 'Card #81'
    element_81:
      '#type': textfield
      '#title': 'Element #81'
  card_82:
    '#type': webform_card
    '#title': 'Card #82'
    element_82:
      '#type': textfield
      '#title': 'Element #82'
  card_83:
    '#type': webform_card
    '#title': 'Card #83'
    element_83:
      '#type': textfield
      '#title': 'Element #83'
  card_84:
    '#type': webform_card
    '#title': 'Card #84'
    element_84:
      '#type': textfield
      '#title': 'Element #84'
  card_85:
    '#type': webform_card
    '#title': 'Card #85'
    element_85:
      '#type': textfield
      '#title': 'Element #85'
  card_86:
    '#type': webform_card
    '#title': 'Card #86'
    element_86:
      '#type': textfield
      '#title': 'Element #86'
  card_87:
    '#type': webform_card
    '#title': 'Card #87'
    element_87:
      '#type': textfield
      '#title': 'Element #87'
  card_88:
    '#type': webform_card
    '#title': 'Card #88'
    element_88:
      '#type': textfield
      '#title': 'Element #88'
  card_89:
    '#type': webform_card
    '#title': 'Card #89'
    element_89:
      '#type': textfield
      '#title': 'Element #89'
  card_90:
    '#type': webform_card
    '#title': 'Card #90'
    element_90:
      '#type': textfield
      '#title': 'Element #90'
  card_91:
    '#type': webform_card
    '#title': 'Card #91'
    element_91:
      '#type': textfield
      '#title': 'Element #91'
  card_92:
    '#type': webform_card
    '#title': 'Card #92'
    element_92:
      '#type': textfield
      '#title': 'Element #92'
  card_93:
    '#type': webform_card
    '#title': 'Card #93'
    element_93:
      '#type': textfield
      '#title': 'Element #93'
  card_94:
    '#type': webform_card
    '#title': 'Card #94'
    element_94:
      '#type': textfield
      '#title': 'Element #94'
  card_95:
    '#type': webform_card
    '#title': 'Card #95'
    element_95:
      '#type': textfield
      '#title': 'Element #95'
  card_96:
    '#type': webform_card
    '#title': 'Card #96'
    element_96:
      '#type': textfield
      '#title': 'Element #96'
  card_97:
    '#type': webform_card
    '#title': 'Card #97'
    element_97:
      '#type': textfield
      '#title': 'Element #97'
  card_98:
    '#type': webform_card
    '#title': 'Card #98'
    element_98:
      '#type': textfield
      '#title': 'Element #98'
  card_99:
    '#type': webform_card
    '#title': 'Card #99'
    element_99:
      '#type': textfield
      '#title': 'Element #99'
  card_100:
    '#type': webform_card
    '#title': 'Card #100'
    element_100:
      '#type': textfield
      '#title': 'Element #100'
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
