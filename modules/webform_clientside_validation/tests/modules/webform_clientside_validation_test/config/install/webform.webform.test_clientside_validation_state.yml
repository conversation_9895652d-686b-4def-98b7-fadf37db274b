uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_clientside_validation_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_clientside_validation_state
title: 'Test: Clientside Validation #states'
description: 'Test webform clientside validation with conditional logic (#states).'
categories:
  - 'Test: Clientside Validation'
elements: |
  trigger:
    '#type': checkbox
    '#title': trigger
  container:
    '#type': container
    '#title': container
    '#states':
      visible:
        ':input[name="trigger"]':
          checked: true
    basic_elements:
      '#type': details
      '#title': 'Basic elements'
      '#open': true
      textfield:
        '#type': textfield
        '#title': textfield
        '#required': true
      textfield_prefix_suffix:
        '#type': textfield
        '#title': textfield_prefix_suffix
        '#field_prefix': '{field_prefix}'
        '#field_suffix': '{field_suffix}'
        '#required': true
      pattern:
        '#type': textfield
        '#title': 'pattern (^[a-z]+$)'
        '#pattern': '^[a-z]+$'
      input_mask:
        '#type': textfield
        '#title': 'input_mask - (999) 999-9999'
        '#input_mask': '(999) 999-9999'
      textarea:
        '#type': textarea
        '#title': textarea
        '#required': true
      select:
        '#type': select
        '#title': select
        '#options':
          one: One
          two: Two
          three: Three
        '#required': true
      checkboxes:
        '#type': checkboxes
        '#title': checkboxes
        '#options':
          one: One
          two: Two
          three: Three
        '#required': true
      radios:
        '#type': radios
        '#title': radios
        '#options':
          one: One
          two: Two
          three: Three
        '#required': true
      checkboxes_two_columns:
        '#type': checkboxes
        '#title': checkboxes_two_columns
        '#options_display': two_columns
        '#options':
          one: One
          two: Two
          three: Three
          four: Four
        '#required': true
    date_elements:
      '#type': details
      '#title': 'Date elements'
      '#open': true
      date:
        '#type': date
        '#title': date
        '#required': true
      datelist:
        '#type': datelist
        '#title': datelist
        '#required': true
      time:
        '#type': webform_time
        '#title': time
        '#required': true
    tableselect_elements:
      '#type': details
      '#title': 'Table select elements'
      '#open': true
      tableselect_checkboxes:
        '#type': tableselect
        '#title': tableselect_checkboxes
        '#required': true
        '#options':
          one: One
          two: Two
          three: Three
          four: Four
          five: Five
      tableselect_radios:
        '#type': tableselect
        '#title': tableselect_radios
        '#multiple': false
        '#required': true
        '#options':
          one: One
          two: Two
          three: Three
          four: Four
          five: Five
    advanced_elements:
      '#type': details
      '#title': 'Advanced elements'
      '#open': true
      email:
        '#type': email
        '#title': email
        '#required': true
      email_multiple:
        '#type': webform_email_multiple
        '#title': email_multiple
        '#required': true
      email_confirm:
        '#type': webform_email_confirm
        '#title': email_confirm
        '#required': true
      tel:
        '#type': tel
        '#title': Telephone
        '#required': true
      tel_international:
        '#type': tel
        '#title': tel_international
        '#international': true
        '#telephone_validation_format': '0'
        '#required': true
      url:
        '#type': url
        '#title': url
        '#required': true
      number:
        '#type': number
        '#title': number
        '#min': 0
        '#max': 10
        '#step': 1
        '#required': true
      range:
        '#type': range
        '#title': range
        '#min': 0
        '#max': 100
        '#step': 1
        '#output': right
        '#output__field_prefix': $
        '#output__field_suffix': '.00'
        '#required': true
      color:
        '#type': color
        '#title': color
        '#required': true
    custom_elements:
      '#type': details
      '#title': 'Custom elements'
      '#open': true
      autocomplete:
        '#type': webform_autocomplete
        '#title': autocomplete
        '#autocomplete_items': country_names
      image_select:
        '#type': webform_image_select
        '#title': image_select
        '#show_label': true
        '#images':
          kitten_1:
            text: 'Cute Kitten 1'
            src: 'http://placekitten.com/220/200'
          kitten_2:
            text: 'Cute Kitten 2'
            src: 'http://placekitten.com/180/200'
          kitten_3:
            text: 'Cute Kitten 3'
            src: 'http://placekitten.com/130/200'
        '#required': true
      rating:
        '#type': webform_rating
        '#title': rating
        '#required': true
      scale:
        '#type': webform_scale
        '#title': scale
        '#min': 1
        '#max': 10
        '#min_text': '1 = disagree'
        '#max_text': '10 = agree'
        '#required': true
      signature:
        '#type': webform_signature
        '#title': signature
        '#required': true
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: both
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
