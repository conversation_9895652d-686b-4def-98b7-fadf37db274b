uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_options_limit_test
  module:
    - webform_options_limit
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_handler_options_limit
title: 'Test: Handler: Test options limit'
description: 'Test webform options limit handler.'
categories: {  }
elements: |
  options_limit_default:
    '#type': checkboxes
    '#title': options_limit_default
    '#default_value':
      - A
      - B
      - C
    '#options':
      none: None
      A: A
      B: B
      C: C
  options_limit_messages:
    '#type': checkboxes
    '#title': options_limit_messages
    '#options':
      none: None
      D: D
      E: E
      F: F
    '#default_value':
      - D
      - E
      - F
  options_limit_select_disable:
    '#type': select
    '#title': options_limit_select_disable
    '#multiple': true
    '#size': 3
    '#options':
      H: H
      I: I
      J: J
    '#default_value':
      - H
      - I
      - J
  options_limit_select_remove:
    '#type': select
    '#title': options_limit_select_remove
    '#multiple': true
    '#size': 3
    '#options':
      K: K
      L: L
      M: M
    '#default_value':
      - K
      - L
      - M
  options_limit_select_none:
    '#type': select
    '#title': options_limit_select_none
    '#multiple': true
    '#size': 3
    '#options':
      O: O
      P: P
      Q: Q
    '#default_value':
      - O
      - P
      - Q
  options_limit_select_other:
    '#type': webform_select_other
    '#title': options_limit_select_other
    '#multiple': true
    '#size': 3
    '#options':
      R: R
      S: S
      T: T
    '#default_value':
      - R
      - S
      - T
  options_limit_tableselect_multiple:
    '#type': tableselect
    '#title': options_limit_tableselect_multiple
    '#options':
      U: U
      V: V
      W: W
    '#default_value':
      - U
      - V
      - W
  options_limit_tableselect_single:
    '#type': tableselect
    '#title': options_limit_tableselect_single
    '#multiple': false
    '#options':
      X: X
      Y: Y
      Z: Z
    '#default_value': X
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: true
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: true
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: message
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  options_limit_default:
    id: options_limit
    label: options_limit_default
    notes: ''
    handler_id: options_limit_default
    status: true
    conditions: {  }
    weight: 0
    settings:
      element_key: options_limit_default
      limits:
        none: 0
        A: 1
        B: 2
        _default_: 3
      limit_reached_message: '@name is not available.'
      limit_source_entity: true
      limit_user: false
      option_none_action: disable
      option_message_display: label
      option_multiple_message: '[@remaining remaining]'
      option_single_message: '[@remaining remaining]'
      option_none_message: '[@remaining remaining]'
      option_unlimited_message: '[Unlimited]'
      option_error_message: '@name: @label is unavailable.'
      tableselect_header: ''
  options_limit_messages:
    id: options_limit
    label: options_limit_messages
    notes: ''
    handler_id: options_limit_messages
    status: true
    conditions: {  }
    weight: 0
    settings:
      element_key: options_limit_messages
      limits:
        none: 0
        D: 1
        E: 2
      limit_reached_message: '@name is not available.'
      limit_source_entity: true
      limit_user: false
      option_none_action: disable
      option_message_display: description
      option_multiple_message: '@remaining options remaining / @limit limit / @total total'
      option_single_message: '@remaining option remaining / @limit limit / @total total'
      option_none_message: 'No options remaining / @limit limit / @total total'
      option_unlimited_message: 'Unlimited / @total total'
      option_error_message: '@name: @label is unavailable.'
      tableselect_header: ''
  options_limit_select_disable:
    id: options_limit
    label: options_limit_select_disable
    notes: ''
    handler_id: options_limit_select_disable
    status: true
    conditions: {  }
    weight: 0
    settings:
      element_key: options_limit_select_disable
      limits:
        H: 1
        I: 2
      limit_reached_message: '@name is not available.'
      limit_source_entity: true
      limit_user: false
      option_none_action: disable
      option_message_display: label
      option_multiple_message: '[@remaining remaining]'
      option_single_message: '[@remaining remaining]'
      option_none_message: '[@remaining remaining]'
      option_unlimited_message: '[Unlimited]'
      option_error_message: '@name: @label is unavailable.'
      tableselect_header: ''
  options_limit_select_remove:
    id: options_limit
    label: options_limit_select_remove
    notes: ''
    handler_id: options_limit_select_remove
    status: true
    conditions: {  }
    weight: 0
    settings:
      element_key: options_limit_select_remove
      limits:
        K: 1
        L: 2
      limit_reached_message: '@name is not available.'
      limit_source_entity: true
      limit_user: false
      option_none_action: remove
      option_message_display: label
      option_multiple_message: '[@remaining remaining]'
      option_single_message: '[@remaining remaining]'
      option_none_message: '[@remaining remaining]'
      option_unlimited_message: '[Unlimited]'
      option_error_message: '@name: @label is unavailable.'
      tableselect_header: ''
  options_limit_select_none:
    id: options_limit
    label: options_limit_select_none
    notes: ''
    handler_id: options_limit_select_none
    status: true
    conditions: {  }
    weight: 0
    settings:
      element_key: options_limit_select_none
      limits:
        O: 1
        P: 2
      limit_reached_message: '@name is not available.'
      limit_source_entity: true
      limit_user: false
      option_none_action: none
      option_message_display: label
      option_multiple_message: '[@remaining remaining]'
      option_single_message: '[@remaining remaining]'
      option_none_message: '[@remaining remaining]'
      option_unlimited_message: '[Unlimited]'
      option_error_message: '@name: @label is unavailable.'
      tableselect_header: ''
  options_limit_select_other:
    id: options_limit
    label: options_limit_select_other
    notes: ''
    handler_id: options_limit_select_other
    status: true
    conditions: {  }
    weight: 0
    settings:
      element_key: options_limit_select_other
      limits:
        R: 1
        S: 2
      limit_reached_message: '@name is not available.'
      limit_source_entity: true
      limit_user: false
      option_none_action: disable
      option_message_display: label
      option_multiple_message: '[@remaining remaining]'
      option_single_message: '[@remaining remaining]'
      option_none_message: '[@remaining remaining]'
      option_unlimited_message: '[Unlimited]'
      option_error_message: '@name: @label is unavailable.'
      tableselect_header: ''
  options_limit_tableselect_multiple:
    id: options_limit
    label: options_limit_tableselect_multiple
    notes: ''
    handler_id: options_limit_tableselect_multiple
    status: true
    conditions: {  }
    weight: 0
    settings:
      element_key: options_limit_tableselect_multiple
      limits:
        U: 1
        V: 2
      limit_reached_message: '@name is not available.'
      limit_source_entity: true
      limit_user: false
      option_none_action: disable
      option_message_display: label
      option_multiple_message: '[@remaining remaining]'
      option_single_message: '[@remaining remaining]'
      option_none_message: '[@remaining remaining]'
      option_unlimited_message: '[Unlimited]'
      option_error_message: '@name: @label is unavailable.'
      tableselect_header: ''
  options_limit_tableselect_single:
    id: options_limit
    label: options_limit_tableselect_single
    notes: ''
    handler_id: options_limit_tableselect_single
    status: true
    conditions: {  }
    weight: 0
    settings:
      element_key: options_limit_tableselect_single
      limits:
        X: 1
        'Y': 2
      limit_reached_message: '@name is not available.'
      limit_source_entity: true
      limit_user: false
      option_none_action: disable
      option_message_display: description
      option_multiple_message: '[@remaining remaining]'
      option_single_message: '[@remaining remaining]'
      option_none_message: '[@remaining remaining]'
      option_unlimited_message: '[Unlimited]'
      option_error_message: '@name: @label is unavailable.'
      tableselect_header: Limits
variants: {  }
