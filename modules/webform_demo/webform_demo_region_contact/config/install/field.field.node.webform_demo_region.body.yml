langcode: en
status: true
dependencies:
  config:
    - field.storage.node.body
    - node.type.webform_demo_region
  module:
    - text
id: node.webform_demo_region.body
field_name: body
entity_type: node
bundle: webform_demo_region
label: Body
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  display_summary: true
field_type: text_with_summary
