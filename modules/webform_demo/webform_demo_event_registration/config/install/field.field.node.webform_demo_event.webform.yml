langcode: en
status: true
dependencies:
  config:
    - field.storage.node.webform
    - node.type.webform_demo_event
  module:
    - webform
id: node.webform_demo_event.webform
field_name: webform
entity_type: node
bundle: webform_demo_event
label: Webform
description: ''
required: true
translatable: true
default_value:
  - default_data: ''
    status: open
    open: ''
    close: ''
    target_uuid: ''
default_value_callback: ''
settings:
  handler: 'default:webform'
  handler_settings:
    target_bundles: null
    auto_create: false
field_type: webform
