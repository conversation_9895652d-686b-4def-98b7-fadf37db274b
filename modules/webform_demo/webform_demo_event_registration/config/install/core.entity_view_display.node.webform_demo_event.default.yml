langcode: en
status: true
dependencies:
  config:
    - field.field.node.webform_demo_event.body
    - field.field.node.webform_demo_event.field_webform_demo_event_date
    - field.field.node.webform_demo_event.field_webform_entity_limit_total
    - field.field.node.webform_demo_event.webform
    - node.type.webform_demo_event
  module:
    - datetime
    - text
    - user
    - webform
id: node.webform_demo_event.default
targetEntityType: node
bundle: webform_demo_event
mode: default
content:
  body:
    label: hidden
    type: text_default
    weight: 1
    settings: {  }
    third_party_settings: {  }
    region: content
  field_webform_demo_event_date:
    weight: 0
    label: above
    settings:
      timezone_override: ''
      format_type: long
    third_party_settings: {  }
    type: datetime_default
    region: content
  field_webform_entity_limit_total:
    type: number_integer
    weight: 2
    region: content
    label: above
    settings:
      thousand_separator: ''
      prefix_suffix: true
    third_party_settings: {  }
  links:
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  webform:
    weight: 3
    label: hidden
    settings:
      label: Register
      dialog: normal
      attributes:
        class:
          - button
        style: 'margin: 1em 0'
    third_party_settings: {  }
    type: webform_entity_reference_link
    region: content
hidden: {  }
