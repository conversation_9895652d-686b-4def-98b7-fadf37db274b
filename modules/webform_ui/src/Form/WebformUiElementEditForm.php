<?php

namespace Drupal\webform_ui\Form;

use <PERSON><PERSON><PERSON>\Component\Utility\Xss;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Render\Markup;
use <PERSON>upal\Core\Url;
use <PERSON><PERSON>al\webform\Utility\WebformElementHelper;
use <PERSON><PERSON>al\webform\WebformInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

/**
 * Provides an edit webform for a webform element.
 */
class WebformUiElementEditForm extends WebformUiElementFormBase {

  /**
   * {@inheritdoc}
   */
  protected $operation = 'update';

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state, WebformInterface $webform = NULL, $key = NULL, $parent_key = NULL, $type = NULL) {
    $this->element = $webform->getElementDecoded($key);
    if ($this->element === NULL) {
      throw new NotFoundHttpException();
    }

    // Handler changing element type.
    if ($type = $this->getRequest()->get('type')) {
      $webform_element = $this->getWebformElementPlugin();
      $related_types = $webform_element->getRelatedTypes($this->element);
      if (!isset($related_types[$type])) {
        throw new NotFoundHttpException();
      }
      $this->originalType = $this->element['#type'];
      $this->element['#type'] = $type;
    }

    // Issue: #title is display as modal dialog's title and can't be escaped.
    // Workaround: Filter and define @title as safe markup.
    $title = WebformElementHelper::getAdminTitle($this->element);
    $form['#title'] = $this->t('Edit @title element', [
      '@title' => Markup::create(Xss::filterAdmin($title)),
    ]);

    $this->action = $this->t('updated');

    $form = parent::buildForm($form, $form_state, $webform, $key);

    // Delete action.
    if (!$form_state->get('default_value_element')) {
      $url = new Url('entity.webform_ui.element.delete_form', ['webform' => $webform->id(), 'key' => $key]);
      $this->buildDialogDeleteAction($form, $form_state, $url);
    }

    return $form;
  }

}
