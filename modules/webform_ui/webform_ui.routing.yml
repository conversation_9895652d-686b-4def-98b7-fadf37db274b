# Elements.

entity.webform.source_form:
  path: '/admin/structure/webform/manage/{webform}/source'
  defaults:
    _title_callback: '\Drupal\webform\Controller\WebformEntityController::title'
    _entity_form: 'webform.source'
  requirements:
    _custom_access: '\Drupal\webform_ui\Access\WebformUiAccess::checkWebformSourceAccess'

# Options.

entity.webform_options.source_form:
  path: '/admin/structure/webform/options/manage/{webform_options}/source'
  defaults:
    _entity_form: 'webform_options.source'
  requirements:
    _custom_access: '\Drupal\webform_ui\Access\WebformUiAccess::checkWebformOptionSourceAccess'

# Element.

entity.webform_ui.element:
  path: '/admin/structure/webform/manage/{webform}/element/add'
  defaults:
    _form: '\Drupal\webform_ui\Form\WebformUiElementTypeSelectForm'
    _title: 'Select an element'
  requirements:
    _custom_access: '\Drupal\webform_ui\Access\WebformUiAccess::checkWebformEditAccess'

entity.webform_ui.change_element:
  path: '/admin/structure/webform/manage/{webform}/element/{key}/change'
  defaults:
    _form: '\Drupal\webform_ui\Form\WebformUiElementTypeChangeForm'
    _title: 'Select new element type'
  requirements:
    _custom_access: '\Drupal\webform_ui\Access\WebformUiAccess::checkWebformEditAccess'

entity.webform_ui.element.add_form:
  path: '/admin/structure/webform/manage/{webform}/element/add/{type}'
  defaults:
    _form: '\Drupal\webform_ui\Form\WebformUiElementAddForm'
    _title: 'Add element'
  requirements:
    _custom_access: '\Drupal\webform_ui\Access\WebformUiAccess::checkWebformElementAccess'

entity.webform_ui.element.add_page:
  path: '/admin/structure/webform/manage/{webform}/element/add/page'
  defaults:
    _form: '\Drupal\webform_ui\Form\WebformUiElementAddForm'
    _title: 'Add page'
    type: webform_wizard_page
  requirements:
    _custom_access: '\Drupal\webform_ui\Access\WebformUiAccess::checkWebformElementAccess'

entity.webform_ui.element.add_layout:
  path: '/admin/structure/webform/manage/{webform}/element/add/layout'
  defaults:
    _form: '\Drupal\webform_ui\Form\WebformUiElementAddForm'
    _title: 'Add layout'
    type: webform_flexbox
  requirements:
    _custom_access: '\Drupal\webform_ui\Access\WebformUiAccess::checkWebformElementAccess'

entity.webform_ui.element.edit_form:
  path: '/admin/structure/webform/manage/{webform}/element/{key}/edit'
  defaults:
    _form: '\Drupal\webform_ui\Form\WebformUiElementEditForm'
    _title: 'Edit element'
  requirements:
    _custom_access: '\Drupal\webform_ui\Access\WebformUiAccess::checkWebformEditAccess'

entity.webform_ui.element.duplicate_form:
  path: '/admin/structure/webform/manage/{webform}/element/{key}/duplicate'
  defaults:
    _form: '\Drupal\webform_ui\Form\WebformUiElementDuplicateForm'
    _title: 'Duplicate element'
  requirements:
    _custom_access: '\Drupal\webform_ui\Access\WebformUiAccess::checkWebformEditAccess'

entity.webform_ui.element.delete_form:
  path: '/admin/structure/webform/manage/{webform}/element/{key}/delete'
  defaults:
    _form: '\Drupal\webform_ui\Form\WebformUiElementDeleteForm'
    _title: 'Delete element'
  requirements:
    _custom_access: '\Drupal\webform_ui\Access\WebformUiAccess::checkWebformEditAccess'

webform.reports_plugins.elements.test:
  path: '/admin/reports/webform-plugins/elements/{type}/test'
  defaults:
    _form: '\Drupal\webform_ui\Form\WebformUiElementTestForm'
    _title: 'Test element'
  requirements:
    _permission: 'administer webform'
