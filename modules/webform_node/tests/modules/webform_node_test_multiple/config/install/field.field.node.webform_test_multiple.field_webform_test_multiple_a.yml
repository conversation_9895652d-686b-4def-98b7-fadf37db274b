langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_webform_test_multiple_a
    - node.type.webform_test_multiple
  module:
    - webform
id: node.webform_test_multiple.field_webform_test_multiple_a
field_name: field_webform_test_multiple_a
entity_type: node
bundle: webform_test_multiple
label: webform_test_multiple_a
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:webform'
  handler_settings:
    target_bundles: null
    auto_create: false
  default_data: ''
  status: open
  open: ''
  close: ''
field_type: webform
