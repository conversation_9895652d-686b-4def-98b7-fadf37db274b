langcode: en
status: true
dependencies:
  config:
    - field.field.node.webform.body
    - field.field.node.webform.webform
    - node.type.webform
  module:
    - text
    - user
    - webform
id: node.webform.default
targetEntityType: node
bundle: webform
mode: default
content:
  body:
    label: hidden
    type: text_default
    weight: 101
    settings: {  }
    third_party_settings: {  }
  links:
    weight: 100
    settings: {  }
    third_party_settings: {  }
  webform:
    weight: 102
    label: hidden
    settings: {  }
    third_party_settings: {  }
    type: webform_entity_reference_entity_view
hidden: {  }
