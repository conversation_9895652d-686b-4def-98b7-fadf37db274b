features.export:
  path: '/admin/config/development/features'
  defaults:
    _form: '\Drupal\features_ui\Form\FeaturesExportForm'
    _title: 'Features'
  requirements:
    _permission: 'export configuration'

features.assignment:
  path: '/admin/config/development/features/bundle/{bundle_name}'
  defaults:
    _form: '\Drupal\features_ui\Form\AssignmentConfigureForm'
    _title: 'Bundle assignment'
    bundle_name: NULL
  requirements:
    _permission: 'administer site configuration'

features.assignment_alter:
  path: '/admin/config/development/features/bundle/_alter/{bundle_name}'
  defaults:
    _form: '\Drupal\features_ui\Form\AssignmentAlterForm'
    _title: 'Configure package configuration altering'
    bundle_name: NULL
  requirements:
    _permission: 'administer site configuration'

features.assignment_base:
  path: '/admin/config/development/features/bundle/_base/{bundle_name}'
  defaults:
    _form: '\Drupal\features_ui\Form\AssignmentBaseForm'
    _title: 'Configure base package assignment'
    bundle_name: NULL
  requirements:
    _permission: 'administer site configuration'

features.assignment_core:
  path: '/admin/config/development/features/bundle/_core/{bundle_name}'
  defaults:
    _form: '\Drupal\features_ui\Form\AssignmentCoreForm'
    _title: 'Configure core package assignment'
    bundle_name: NULL
  requirements:
    _permission: 'administer site configuration'

features.assignment_exclude:
  path: '/admin/config/development/features/bundle/_exclude/{bundle_name}'
  defaults:
    _form: '\Drupal\features_ui\Form\AssignmentExcludeForm'
    _title: 'Configure package exclusion'
    bundle_name: NULL
  requirements:
    _permission: 'administer site configuration'

features.assignment_optional:
  path: '/admin/config/development/features/bundle/_optional/{bundle_name}'
  defaults:
    _form: '\Drupal\features_ui\Form\AssignmentOptionalForm'
    _title: 'Configure optional package assignment'
    bundle_name: NULL
  requirements:
    _permission: 'administer site configuration'

features.assignment_profile:
  path: '/admin/config/development/features/bundle/_profile/{bundle_name}'
  defaults:
    _form: '\Drupal\features_ui\Form\AssignmentProfileForm'
    _title: 'Configure profile package assignment'
    bundle_name: NULL
  requirements:
    _permission: 'administer site configuration'

features.assignment_site:
  path: '/admin/config/development/features/bundle/_site/{bundle_name}'
  defaults:
    _form: '\Drupal\features_ui\Form\AssignmentSiteForm'
    _title: 'Configure site package assignment'
    bundle_name: NULL
  requirements:
    _permission: 'administer site configuration'

features.edit:
  path: '/admin/config/development/features/edit/{featurename}'
  defaults:
    _form: '\Drupal\features_ui\Form\FeaturesEditForm'
    _title: 'Edit'
    featurename: ''
  requirements:
    _permission: 'administer site configuration'

features.diff:
  path: '/admin/config/development/features/diff/{featurename}'
  defaults:
    _form: '\Drupal\features_ui\Form\FeaturesDiffForm'
    _title: 'Differences'
    featurename: ''
  requirements:
    _permission: 'administer site configuration'

features.detect:
  path: '/features/api/detect/{name}'
  defaults:
    _controller: '\Drupal\features_ui\Controller\FeaturesUIController::detect'
  requirements:
    _permission: 'administer site configuration'
