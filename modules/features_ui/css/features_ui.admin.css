span.features-item-list span {
  background: #eee;
  border-radius: 5px;
  margin-right: 5px;
  padding: 2px 5px;
  white-space: nowrap;
  line-height: 1.7em;
}

.features-listing span.features-override,
.features-listing a.features-override {
  background: #ffce6f;
  border-radius: 5px;
  margin-right: 5px;
  padding: 2px 5px;
  white-space: nowrap;
}

.features-listing span.features-detected,
.features-listing a.features-detected {
  color: #68a;
  background: #def;
  border-radius: 5px;
  margin-right: 5px;
  padding: 2px 5px;
  white-space: nowrap;
}

.features-listing span.features-conflict,
.features-listing a.features-conflict {
  color: #fff;
  background-color: #c30 !important;
  border-radius: 5px;
  margin-right: 5px;
  padding: 2px 5px;
  white-space: nowrap;
}

.features-listing span.features-moved,
.features-listing a.features-moved {
  color: #fff;
  background-color: #215900 !important;
  border-radius: 5px;
  margin-right: 5px;
  padding: 2px 5px;
  white-space: nowrap;
}

.features-listing span.features-missing,
.features-listing a.features-missing {
  color: #fff;
  background-color: #0e5ea9 !important;
  border-radius: 5px;
  margin-right: 5px;
  padding: 2px 5px;
  white-space: nowrap;
}

table.features-listing .column-nowrap {
  white-space: nowrap;
}

tr.features-export-header-row td {
  border-top: 30px solid white;
  background: #eee;
  padding-top: 10px;
}

#features-assignment-methods tr.draggable > td:nth-child(1) {
  white-space: nowrap;
}

/** Styles for Features Export/Listing page **/
.features-listing td {
  vertical-align: top;
}

.features-listing td.feature-name {
  font-size: 14px;
  white-space: nowrap;
}

.features-listing td details {
  border: 0;
  margin: 0;
  height: 20px;
}

.features-listing details[open] {
  height: auto;
  overflow: visible;
  white-space: normal;
}

.features-listing td summary {
  padding: 0;
  text-transform: none;
  font-weight: normal;
}

.features-listing td .features-item-label {
  font-weight: bold;
}

.features-header > div,
.features-header > input {
  display: inline-block;
}

/** Styles for Features Edit page **/

.fieldset-legend {
  font-size: 14px;
}

.features-export-info {
  width: 49%;
  float: left; /* LTR */
  position: relative;
}
[dir="rtl"] .features-export-info {
  float: right;
}

.features-export-wrapper {
  width: 49%;
  float: right; /* LTR */
  clear: both;
  position: relative;
}
[dir="rtl"] .features-export-wrapper {
  float: left;
}

.features-ui-conflicts {
  clear: left; /* LTR */
}
[dir="rtl"] .features-ui-conflicts {
  clear: right;
}

.features-export-list {
  font-weight: normal;
  font-size: 12px;
  border: 1px solid #ccc;
  border-top-width: 0;
  overflow: hidden;
  padding: 0 10px 0 30px;
}

span.features-component-list span {
  white-space: nowrap;
  margin-right: 5px;
  padding: 2px 5px;
  background: #eee;
  border-radius: 5px;
}

.features-export-empty {
  display: none;
}

span.features-component-list span.features-conflict {
  background-color: #c30 !important;
  color: #fff;
}

span.features-component-list .features-detected {
  color: #68a;
  background: #def;
}

span.features-component-list .features-dependency {
  color: #999;
  background: #f8f8f8;
}

.features-legend-component {
  font-style: normal;
  color: black;
  display: inline-block;
  background: transparent;
  border: 1px solid #ddd;
  border-radius: 5px;
  white-space: nowrap;
  padding: 0 8px;
  margin: 0 10px 0 0; /* LTR */
}
[dir="rtl"] .features-legend-component {
  margin-right: 0;
  margin-left: 10px;
}

#features-export-wrapper .config-name {
  color: #777;
}
#features-export-wrapper .components-detected .form-item,
.features-legend-component--detected {
  font-style: italic;
  color: #68a;
  background: #def;
  border-width: 0;
}
#features-export-wrapper .components-added .form-item,
.features-legend-component--added {
  font-weight: bold;
  background: #eee;
  border-width: 0;
}
#features-export-wrapper .form-item.component-conflict,
.features-legend-component--conflict {
  color: #c30 !important;
  font-weight: bold !important;
}

#features-export-wrapper .component-conflict.form-item label,
#features-export-wrapper .components-added .form-item label {
  font-weight: bold !important;
}

.features-diff-header-action {
  font-size: small;
}

.js-features-filter-hidden,
.js-features-diff-hidden {
  display: none !important;
}

.features-filter .form-text {
  width: 200px;
}
.features-filter .fieldset-content,
.features-filter .fieldset-wrapper,
.features-filter .features-filter__fieldset {
  border: 0;
  padding: 0;
  margin: 0;
  box-shadow: none;
}
.features-filter .features-filter__fieldset legend {
  display: none;
}

.features-filter-clear {
  margin: 5px 5px;
  padding: 0 5px;
  background: #eee;
  border-radius: 5px;
  cursor: pointer;
}
.features-filter-clear:hover {
  background: #def;
}

.features-export-parent {
  clear: both;
  margin: 0 0 10px;
}

details.features-export-component {
  background: #f3f8fb;
  margin: 0;
  padding: 0 5px;
}
details.features-export-component summary {
  white-space: nowrap;
  text-transform: none;
  font-weight: normal;
  font-size: 14px;
}
details.features-export-component summary .component-count {
  font-size: 11px;
}
details.features-export-component .details-wrapper {
  padding-left: 25px;
  padding-top: 0;
}
[dir="rtl"] details.features-export-component .details-wrapper {
  padding-right: 25px;
  padding-left: 0;
}

/** Styles for Bundle assignment config form **/
#edit-bundles-wrapper .form-item-bundle-bundle-select {
  display: inline-block;
}

#edit-bundles-wrapper #edit-bundle-remove {
  display: inline-block;
  font-size: 10px;
}

/** Styles for plugin config forms **/
.features-assignment-settings-form .fieldset-wrapper {
  padding-left: 16px;
}
[dir="rtl"] .features-assignment-settings-form .fieldset-wrapper {
  padding-right: 16px;
  padding-left: 0;
}
