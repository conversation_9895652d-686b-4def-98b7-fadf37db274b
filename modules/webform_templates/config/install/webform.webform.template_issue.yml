uuid: null
langcode: en
status: closed
dependencies:
  enforced:
    module:
      - webform_templates
open: null
close: null
weight: 0
uid: null
template: true
archive: false
id: template_issue
title: Issue
description: 'An issue webform template.'
categories: {  }
elements: |
  meta_data_details:
    '#title': 'Issue Meta Data'
    '#type': details
    '#open': true
    meta_data_01:
      '#type': webform_flexbox
      title:
        '#type': textfield
        '#title': Title
        '#required': true
      project:
        '#type': select
        '#title': Project
        '#required': true
        '#options':
          'Project I': 'Project I'
          'Project II': 'Project II'
          'Project III': 'Project III'
    meta_data_02:
      '#type': webform_flexbox
      category:
        '#type': select
        '#title': Category
        '#required': true
        '#options':
          'Bug report': 'Bug report'
          Task: Task
          'Feature request': 'Feature request'
          'Support request': 'Support request'
          Plan: Plan
      priority:
        '#type': select
        '#title': Priority
        '#required': true
        '#options':
          4: Critical
          3: Major
          2: Normal
          1: Minor
      status:
        '#type': webform_select_other
        '#title': Status
        '#required': true
        '#options':
          Active: Active
          'Needs work': 'Needs work'
          'Needs review': 'Needs review'
          'Reviewed & tested by the community': 'Reviewed & tested by the community'
          'Patch (to be ported)': 'Patch (to be ported)'
          Fixed: Fixed
          Postponed: Postponed
          'Postponed (maintainer needs more info)': 'Postponed (maintainer needs more info)'
          'Closed (duplicate)': 'Closed (duplicate)'
          'Closed (won''t fix)': 'Closed (won''t fix)'
          'Closed (works as designed)': 'Closed (works as designed)'
          'Closed (cannot reproduce)': 'Closed (cannot reproduce)'
          'Closed (outdated)': 'Closed (outdated)'
      version:
        '#type': webform_autocomplete
        '#title': Version
        '#required': true
        '#autocomplete_existing': true
      component:
        '#type': webform_select_other
        '#title': Component
        '#required': true
        '#options':
          Code: Code
          Documentation: Documentation
          Miscellaneous: Miscellaneous
          'User interface': 'User interface'
      assigned:
        '#type': entity_autocomplete
        '#title': Assigned
        '#target_type': user
        '#selection_handler': 'default:user'
        '#selection_settings':
          include_anonymous: false
    meta_data_03:
      '#type': webform_flexbox
      tags:
        '#type': entity_autocomplete
        '#title': 'Issue Tags'
        '#description': 'Do <strong>NOT</strong> use tags for adding random keywords or duplicating any other fields.  Separate terms with a comma, not a space.'
        '#tags': true
        '#target_type': taxonomy_term
        '#selection_handler': 'default:taxonomy_term'
        '#selection_settings':
          target_bundles:
            tags: tags
          auto_create: 1
          auto_create_bundle: tags
  summary_and_relationships_details:
    '#title': 'Issue Summary'
    '#type': details
    '#open': true
    summary:
      '#type': text_format
      '#title': Summary
      '#description': 'An issue summary is a concise overview of a full issue report. Issue summaries need to be written if the issue has more than a few comments and/or an average developer cannot understand the subject matter after a few minutes of study. These summaries are key sources of information for core developers, patch reviewers and users who need to skim large amounts of issues and information quickly.'
  files_details:
    '#title': Files
    '#type': details
    '#open': true
    files:
      '#type': managed_file
      '#title': Files
      '#multiple': true
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: false
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 1
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
