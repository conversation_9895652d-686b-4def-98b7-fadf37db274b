uuid: null
langcode: en
status: closed
dependencies:
  enforced:
    module:
      - webform_templates
open: null
close: null
weight: 0
uid: null
template: true
archive: false
id: template_user_profile
title: 'User Profile'
description: 'A user profile webform template.'
categories: {  }
elements: |
  account_information:
    '#title': 'Your Account Information'
    '#type': webform_section
    user_name:
      '#type': textfield
      '#title': 'User Name'
    picture:
      '#type': managed_file
      '#title': Picture
      '#description': 'Your virtual face or picture. Pictures larger than 1024x1024 pixels will be scaled down.'
      '#max_filesize': '2'
      '#file_extensions': 'gif jpg png svg'
  personal_information:
    '#title': 'Your Personal Information'
    '#type': webform_section
    first_name:
      '#title': 'First Name'
      '#type': textfield
      '#required': true
    last_name:
      '#type': textfield
      '#title': 'Last Name'
      '#required': true
    country:
      '#type': select
      '#title': Country
      '#options': country_names
    languages:
      '#type': select
      '#title': 'Languages Spoken'
      '#description': 'Select one or more languages you speak.'
      '#multiple': true
      '#select2': true
      '#options': languages
    sex:
      '#type': webform_radios_other
      '#title': Sex
      '#options': sex
    biography:
      '#type': text_format
      '#title': Biography
      '#description': 'Please include a short blurb about yourself to let us know who you are outside of Drupal.'
    website:
      '#type': url
      '#title': Website
    irc:
      '#type': textfield
      '#title': IRC
      '#description': 'The nickname you use on various channels of irc.freenode.net'
    twitter:
      '#type': textfield
      '#title': Twitter
      '#description': 'Your Twitter handle.'
    github:
      '#type': textfield
      '#title': GitHub
      '#description': 'Your GitHub user name.'
  work_information:
    '#title': 'Your Work Information'
    '#type': webform_section
    current_organization:
      '#type': textfield
      '#title': 'Current Organization'
    current_title:
      '#type': textfield
      '#title': 'Current Job Title'
    organizations:
      '#type': textarea
      '#title': 'Past Organizations'
      '#description': 'List companies or institutions you have worked for.'
    industries:
      '#type': webform_select_other
      '#title': 'Industries Worked In'
      '#options': industry
      '#multiple': true
      '#select2': true
  email_settings:
    '#title': 'Email addresses'
    '#type': webform_section
    email:
      '#type': email
      '#title': 'Primary Email Address'
      '#description': 'Enter your primary email addresses, which will be used for all email communications.'
    emails:
      '#type': webform_email_multiple
      '#title': 'Secondary Email Addresses'
      '#description': 'Enter multiple email addresses separated by commas.'
  regional_settings:
    '#title': 'Regional Settings'
    '#type': webform_section
    time_zone:
      '#type': select
      '#title': Timezone
      '#options': time_zones
    language:
      '#type': select
      '#title': 'Preferred Language'
      '#description': 'This account''s default language for emails, and preferred language for site presentation.'
      '#options': languages
  actions:
    '#type': webform_actions
    '#title': 'Submit button(s)'
    '#submit__label': Register
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  email_notification:
    id: email
    label: 'Email Notification'
    notes: ''
    handler_id: email_notification
    status: true
    conditions: {  }
    weight: 1
    settings:
      states:
        - completed
      to_mail: _default
      to_options: {  }
      cc_mail: ''
      cc_options: {  }
      bcc_mail: ''
      bcc_options: {  }
      from_mail: '[webform_submission:values:email:raw]'
      from_options: {  }
      from_name: '[webform_submission:values:first_name] [webform_submission:values:last_name]'
      subject: _default
      body: _default
      excluded_elements: {  }
      ignore_access: false
      exclude_empty: true
      exclude_empty_checkbox: false
      exclude_attachments: false
      html: true
      attachments: false
      twig: false
      theme_name: ''
      parameters: {  }
      debug: false
      reply_to: ''
      return_path: ''
      sender_mail: ''
      sender_name: ''
variants: {  }
