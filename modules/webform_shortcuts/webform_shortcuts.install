<?php

/**
 * @file
 * Install, update and uninstall functions for the Webform shortcuts module.
 */

/**
 * Issue #3040822: Allow keyboard shortcuts to be customized or disabled.
 */
function webform_shortcuts_update_8001() {
  \Drupal::configFactory()
    ->getEditable('webform_shortcuts.settings')
    ->setData([
      'add_element' => 'ctrl+e',
      'add_page' => 'ctrl+p',
      'add_layout' => 'ctrl+l',
      'save_elements' => 'ctrl+s',
      'reset_elements' => 'ctrl+r',
      'toggle_weights' => 'ctrl+w',
    ])->save();
}
