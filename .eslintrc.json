{"extends": "eslint:recommended", "root": true, "env": {"browser": true}, "globals": {"Drupal": true, "drupalSettings": true, "drupalTranslations": true, "domready": true, "jQuery": true, "_": true, "matchMedia": true, "Backbone": true, "Modernizr": true, "CKEDITOR": true}, "rules": {"array-bracket-spacing": ["error", "never"], "block-scoped-var": "error", "brace-style": ["error", "stroust<PERSON>", {"allowSingleLine": true}], "comma-dangle": ["error", "never"], "comma-spacing": "error", "comma-style": ["error", "last"], "computed-property-spacing": ["error", "never"], "curly": ["error", "all"], "eol-last": "error", "eqeqeq": ["error", "smart"], "guard-for-in": "error", "indent": ["error", 2, {"SwitchCase": 1}], "key-spacing": ["error", {"beforeColon": false, "afterColon": true}], "keyword-spacing": ["error", {"before": true, "after": true}], "linebreak-style": ["error", "unix"], "lines-around-comment": ["error", {"beforeBlockComment": true, "afterBlockComment": false}], "new-parens": "error", "no-array-constructor": "error", "no-caller": "error", "no-catch-shadow": "error", "no-eval": "error", "no-extend-native": "error", "no-extra-bind": "error", "no-extra-parens": ["error", "functions"], "no-implied-eval": "error", "no-iterator": "error", "no-label-var": "error", "no-labels": "error", "no-lone-blocks": "error", "no-loop-func": "error", "no-multi-spaces": "error", "no-multi-str": "error", "no-native-reassign": "error", "no-nested-ternary": "error", "no-new-func": "error", "no-new-object": "error", "no-new-wrappers": "error", "no-octal-escape": "error", "no-process-exit": "error", "no-proto": "error", "no-return-assign": "error", "no-script-url": "error", "no-sequences": "error", "no-shadow-restricted-names": "error", "no-spaced-func": "error", "no-trailing-spaces": "error", "no-undef-init": "error", "no-undefined": "error", "no-unused-expressions": "error", "no-unused-vars": ["error", {"vars": "all", "args": "none"}], "no-with": "error", "object-curly-spacing": ["error", "never"], "one-var": ["error", "never"], "quote-props": ["error", "consistent-as-needed"], "quotes": ["error", "single", "avoid-escape"], "semi": ["error", "always"], "semi-spacing": ["error", {"before": false, "after": true}], "space-before-blocks": ["error", "always"], "space-before-function-paren": ["error", {"anonymous": "always", "named": "never"}], "space-in-parens": ["error", "never"], "space-infix-ops": "error", "space-unary-ops": ["error", {"words": true, "nonwords": false}], "spaced-comment": ["error", "always"], "strict": ["error", "function"], "yoda": ["error", "never"], "max-nested-callbacks": ["warn", 3], "valid-jsdoc": ["warn", {"prefer": {"returns": "return", "property": "prop"}, "requireReturn": false}]}}