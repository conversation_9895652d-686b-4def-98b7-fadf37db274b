
Welcome to Pa11y

 > Running Pa11y on URL http://localhost/wf/webform/example_accessibility_advanced

Results for URL: http://localhost/wf/webform/example_accessibility_advanced

 • Error: This searchinput element does not have a name available to an accessibility API. Valid names are: label element, title undefined, aria-label undefined, aria-labelledby undefined.
   ├── WCAG2AA.Principle4.Guideline4_1.4_1_2.H91.InputSearch.Name
   ├── #edit-option-elements > div > div:nth-child(2) > span > span:nth-child(1) > span > ul > li > input
   └── <input class="select2-search__field" type="search" tabindex="0" autocomplete="off" autocorrect="off" autocapitalize="none" spellcheck="false" role="searchbox" aria-autocomplete="list" placeholder="" style="width: 0.75em;">

 • Error: Duplicate id attribute value "iti-item-gb" found on the web page.
   ├── WCAG2AA.Principle4.Guideline4_1.4_1_1.F77
   ├── #iti-item-gb
   └── <li class="iti__country iti__standard" tabindex="-1" id="iti-item-gb" role="option" data-dial-code="44" data-country-code="gb"><div class="iti__flag-box"><div...</li>

 • Error: Duplicate id attribute value "iti-item-us" found on the web page.
   ├── WCAG2AA.Principle4.Guideline4_1.4_1_1.F77
   ├── #iti-item-us
   └── <li class="iti__country iti__standard" tabindex="-1" id="iti-item-us" role="option" data-dial-code="1" data-country-code="us"><div class="iti__flag-box"><div...</li>

 • Error: This link points to a named anchor "terms" within the document, but no anchor exists with that name.
   ├── WCAG2AA.Principle2.Guideline2_4.2_4_1.G1,G123,G124.NoSuchID
   ├── #edit-widget-elements > div > div:nth-child(4) > label > a
   └── <a role="button" href="#terms">terms of service</a>

4 Errors

