<?php

namespace Drupal\Tests\webform\Functional\Handler;

use Drupal\Tests\webform\Functional\WebformBrowserTestBase;
use Drupal\webform\Entity\Webform;

/**
 * Tests for email webform handler email roles functionality.
 *
 * @group webform
 */
class WebformHandlerEmailRolesTest extends WebformBrowserTestBase {

  /**
   * Webforms to load.
   *
   * @var array
   */
  protected static $testWebforms = ['test_handler_email_roles'];

  /**
   * Test email roles handler.
   */
  public function testEmailRoles() {
    $assert_session = $this->assertSession();

    // Enable all authenticated roles.
    \Drupal::configFactory()
      ->getEditable('webform.settings')
      ->set('mail.roles', ['authenticated'])
      ->save();

    // IMPORTANT: Simpletest create 'administrators' role while Drupal
    // creates 'administrator' role.
    // WORKAROUND: Create 'administrator' role so that SimpleT<PERSON> and Drupal
    // are in-sync.
    $this->drupalCreateRole([], 'administrator');

    $webform = Webform::load('test_handler_email_roles');

    $authenticated_user = $this->drupalCreateUser();
    $authenticated_user->set('mail', '<EMAIL>');
    $authenticated_user->save();

    $blocked_user = $this->drupalCreateUser();
    $blocked_user->set('mail', '<EMAIL>');
    $blocked_user->block();
    $blocked_user->save();

    $admin_user = $this->drupalCreateUser();
    $admin_user->set('mail', '<EMAIL>');
    $admin_user->addRole('administrator');
    $admin_user->save();

    // Check email all authenticated users.
    $this->postSubmission($webform, ['role' => 'authenticated']);
    $assert_session->responseContains('<em class="placeholder">Webform submission from: Test: Handler: Email roles</em> sent to <em class="placeholder"><EMAIL>,<EMAIL>,<EMAIL></em> from <em class="placeholder">Drupal</em> [<em class="placeholder"><EMAIL></em>].');

    // Check that blocked user is never emailed.
    $assert_session->responseNotContains('<EMAIL>');

    // Check that unblocked user is never emailed.
    $blocked_user->activate()->save();
    $this->postSubmission($webform, ['role' => 'authenticated']);
    $assert_session->responseContains('<EMAIL>');

    // Check email administrator user.
    $this->postSubmission($webform, ['role' => 'administrator']);
    $assert_session->responseContains('<em class="placeholder">Webform submission from: Test: Handler: Email roles</em> sent to <em class="placeholder"><EMAIL></em> from <em class="placeholder">Drupal</em> [<em class="placeholder"><EMAIL></em>].');

    // Check that missing 'other' role does not send any emails.
    $this->postSubmission($webform, ['role' => 'other']);
    $assert_session->responseContains('<em class="placeholder">Test: Handler: Email roles</em>: Email not sent for <em class="placeholder">Email</em> handler because a <em>To</em>, <em>CC</em>, or <em>BCC</em> email was not provided.');

    // Check that authenticated role is no longer available.
    // Enable only administrator role.
    \Drupal::configFactory()
      ->getEditable('webform.settings')
      ->set('mail.roles', ['administrator'])
      ->save();
    $this->postSubmission($webform, ['role' => 'authenticated']);
    $assert_session->responseContains('<em class="placeholder">Test: Handler: Email roles</em>: Email not sent for <em class="placeholder">Email</em> handler because a <em>To</em>, <em>CC</em>, or <em>BCC</em> email was not provided.');
  }

}
