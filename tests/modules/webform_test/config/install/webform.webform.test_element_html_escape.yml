uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_element_html_escape
title: 'Test: Element: HTML escaping'
description: 'Test element HTML escaping support'
categories:
  - 'Test: Element'
elements: |
  basic_elements:
    '#type': details
    '#title': 'Basic elements | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    checkbox:
      '#type': checkbox
      '#title': 'Checkbox | <script>alert(''This markup is not escaped properly!!!'') </script>'
    password:
      '#type': password
      '#title': 'Password | <script>alert(''This markup is not escaped properly!!!'') </script>'
    textarea:
      '#type': textarea
      '#title': 'Textarea | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#rows': 2
    textarea_multiple:
      '#type': textarea
      '#title': 'Textarea multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#rows': 2
      '#multiple': true
    textfield:
      '#type': textfield
      '#title': 'Text field | <script>alert(''This markup is not escaped properly!!!'') </script>'
    textfield_multiple:
      '#type': textfield
      '#title': 'Text field multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
  advanced_elements:
    '#type': details
    '#title': 'Advanced elements | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    webform_autocomplete:
      '#type': webform_autocomplete
      '#title': 'Autocomplete | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_autocomplete_multiple:
      '#type': webform_autocomplete
      '#title': 'Autocomplete multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    captcha:
      '#type': captcha
      '#title': 'CAPTCHA | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#captcha_admin_mode': true
      '#captcha_info':
        form_id: ''
      '#captcha_type': image_captcha/Image
    webform_codemirror:
      '#type': webform_codemirror
      '#title': 'CodeMirror | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#mode': yaml
    color:
      '#type': color
      '#title': 'Color | <script>alert(''This markup is not escaped properly!!!'') </script>'
    email:
      '#type': email
      '#title': 'Email | <script>alert(''This markup is not escaped properly!!!'') </script>'
    email_multiple:
      '#type': email
      '#title': 'Email multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    webform_email_confirm:
      '#type': webform_email_confirm
      '#title': 'Email confirm | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_email_multiple:
      '#type': webform_email_multiple
      '#title': 'Email multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_height:
      '#type': webform_height
      '#title': 'Height (feet/inches) | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_height_multiple:
      '#type': webform_height
      '#title': 'Height (feet/inches) multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    number:
      '#type': number
      '#title': 'Number | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#min': 0
      '#max': 10
      '#step': 1
    number_multiple:
      '#type': number
      '#title': 'Number multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#min': 0
      '#max': 10
      '#step': 1
      '#multiple': true
    password_confirm:
      '#type': password_confirm
      '#title': 'Password confirm | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/1427838">Issue #1427838: password and password_confirm children do not pick up #states or #attributes</a>'
    range:
      '#type': range
      '#title': 'Range | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#min': 0
      '#max': 100
      '#step': 1
      '#output': below
      '#output__field_prefix': $
      '#output__field_suffix': '.00'
    webform_rating:
      '#type': webform_rating
      '#title': 'Rating | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_same:
      '#type': webform_same
      '#title': 'Billing address is the same as the shipping address | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_scale:
      '#type': webform_scale
      '#title': 'Scale | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#min': 1
      '#max': 5
    search:
      '#type': search
      '#title': 'Search | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_signature:
      '#type': webform_signature
      '#title': 'Signature | <script>alert(''This markup is not escaped properly!!!'') </script>'
    tel:
      '#type': tel
      '#title': 'Telephone | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#international': true
    tel_multiple:
      '#type': tel
      '#title': 'Telephone multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#international': true
      '#multiple': true
    webform_terms_of_service:
      '#type': webform_terms_of_service
      '#title': 'I agree to the {terms of service}. | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#required': true
      '#terms_type': slideout
      '#terms_content': '<em>These are the terms of service.</em>'
    url:
      '#type': url
      '#title': 'URL | <script>alert(''This markup is not escaped properly!!!'') </script>'
    url_multiple:
      '#type': url
      '#title': 'URL multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    value:
      '#type': value
      '#title': 'Value | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#value': preview
  markup_elements:
    '#type': details
    '#title': 'Markup elements | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    processed_text:
      '#type': processed_text
      '#title': 'Advanced HTML/Text | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_markup:
      '#type': webform_markup
      '#title': 'Basic HTML | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2700667">Issue #2700667: Notice: Undefined index: #type in drupal_process_states()</a>'
    webform_horizontal_rule:
      '#type': webform_horizontal_rule
      '#attributes':
        class:
          - webform-horizontal-rule--dotted
          - webform-horizontal-rule--thick
    label:
      '#type': label
      '#title': 'Label | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_message:
      '#type': webform_message
      '#title': 'Message | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#message_type': warning
      '#message_message': 'This is a <strong>warning</strong> message.'
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/77245">Issue #77245: A place for JavaScript status messages</a>'
    webform_more:
      '#type': webform_more
      '#title': 'More | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#more': 'This is more content'
  file_attachment_elements:
    '#type': details
    '#title': 'File attachment elements | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    webform_entity_print_attachment_pdf:
      '#type': webform_entity_print_attachment
      '#title': 'Attachment PDF | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_attachment_token:
      '#type': webform_attachment_token
      '#title': 'Attachment token | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_attachment_twig:
      '#type': webform_attachment_twig
      '#title': 'Attachment Twig | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_attachment_url:
      '#type': webform_attachment_url
      '#title': 'Attachment URL | <script>alert(''This markup is not escaped properly!!!'') </script>'
  file_upload_elements:
    '#type': details
    '#title': 'File upload elements | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    webform_audio_file:
      '#type': webform_audio_file
      '#title': 'Audio file | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_audio_file_multiple:
      '#type': webform_audio_file
      '#title': 'Audio file multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    webform_document_file:
      '#type': webform_document_file
      '#title': 'Document file | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_document_file_multiple:
      '#type': webform_document_file
      '#title': 'Document file multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    managed_file:
      '#type': managed_file
      '#title': 'File | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2705471">Issue #2705471: Webform states managed file fields</a><br /><a href="https://www.drupal.org/node/2113931">Issue #2113931: File Field design update</a><br /><a href="https://www.drupal.org/node/2346893">Issue #2346893: Duplicate Ajax wrapper around a file field</a><br /><a href="https://www.drupal.org/node/2482783">Issue #2482783: File upload errors not set or shown correctly</a>'
    managed_file_multiple:
      '#type': managed_file
      '#title': 'File multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    webform_image_file:
      '#type': webform_image_file
      '#title': 'Image file | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_image_file_multiple:
      '#type': webform_image_file
      '#title': 'Image file multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    webform_video_file:
      '#type': webform_video_file
      '#title': 'Video file | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_video_file_multiple:
      '#type': webform_video_file
      '#title': 'Video file multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
  options_elements:
    '#type': details
    '#title': 'Options elements | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    checkboxes:
      '#type': checkboxes
      '#title': 'Checkboxes | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
      '#options_display': side_by_side
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/994360">Issue #994360: #states cannot disable/enable radios and checkboxes</a><br /><a href="https://www.drupal.org/node/2836364">Issue #2836364: Wrapper attributes are not supported by composite elements, this includes radios, checkboxes, and buttons.</a>'
    webform_checkboxes_other:
      '#type': webform_checkboxes_other
      '#title': 'Checkboxes other | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
      '#options_display': side_by_side
    webform_image_select:
      '#type': webform_image_select
      '#title': 'Image select | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#show_label': true
      '#images':
        dog_1:
          text: 'Dog 1'
          src: 'https://www.placedog.net/80/100'
        dog_2:
          text: 'Dog 2'
          src: 'https://www.placedog.net/100/100'
        dog_3:
          text: 'Dog 3'
          src: 'https://www.placedog.net/120/100'
    webform_image_select_multiple:
      '#type': webform_image_select
      '#title': 'Image select multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#show_label': true
      '#images':
        dog_1:
          text: 'Dog 1'
          src: 'https://www.placedog.net/80/100'
        dog_2:
          text: 'Dog 2'
          src: 'https://www.placedog.net/100/100'
        dog_3:
          text: 'Dog 3'
          src: 'https://www.placedog.net/120/100'
      '#multiple': true
    radios:
      '#type': radios
      '#title': 'Radios | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
      '#options_display': side_by_side
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2731991">Issue #2731991: Setting required on radios marks all options required</a><br /><a href="https://www.drupal.org/node/994360">Issue #994360: #states cannot disable/enable radios and checkboxes</a><br /><a href="https://www.drupal.org/node/2836364">Issue #2836364: Wrapper attributes are not supported by composite elements, this includes radios, checkboxes, and buttons.</a>'
    webform_radios_other:
      '#type': webform_radios_other
      '#title': 'Radios other | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
      '#options_display': side_by_side
    select:
      '#type': select
      '#title': 'Select | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/1426646">Issue #1426646: "-Select-" option is lost when webform elements uses ''#states''</a><br /><a href="https://www.drupal.org/node/1149078">Issue #1149078: States API doesn''t work with multiple select fields</a><br /><a href="https://www.drupal.org/node/2791741">Issue #2791741: FAPI states: fields aren''t hidden initially when depending on multi-value selection</a>'
    select_multiple:
      '#type': select
      '#title': 'Select multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
      '#multiple': true
      '#select2': true
    webform_select_other:
      '#type': webform_select_other
      '#title': 'Select other | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
    webform_select_other_multiple:
      '#type': webform_select_other
      '#title': 'Select other multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
      '#multiple': true
      '#select2': true
    tableselect:
      '#type': tableselect
      '#title': 'Table select | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
    webform_tableselect_sort:
      '#type': webform_tableselect_sort
      '#title': 'Tableselect sort | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
    webform_table_sort:
      '#type': webform_table_sort
      '#title': 'Table sort | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options':
        one: One
        two: Two
        three: Three
  computed_elements:
    '#type': details
    '#title': 'Computed Elements | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    webform_computed_token:
      '#type': webform_computed_token
      '#title': 'Computed token | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#template': 'This is a Computed token value.'
    webform_computed_twig:
      '#type': webform_computed_twig
      '#title': 'Computed Twig | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#template': 'This is a Computed Twig value.'
  containers:
    '#type': details
    '#title': 'Containers | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    container:
      '#type': container
      '#title': 'Container | <script>alert(''This markup is not escaped properly!!!'') </script>'
    details:
      '#type': details
      '#title': 'Details | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2348851">Issue #2348851: Regression: Allow HTML tags inside detail summary</a>'
    fieldset:
      '#type': fieldset
      '#title': 'Fieldset | <script>alert(''This markup is not escaped properly!!!'') </script>'
    item:
      '#type': item
      '#title': 'Item | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#markup': '{markup}'
      '#field_prefix': '{field_prefix} | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#field_suffix': '{field_suffix} | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/783438">Issue #783438: #states doesn''t work for #type item</a>'
    webform_section:
      '#type': webform_section
      '#title': 'Section | <script>alert(''This markup is not escaped properly!!!'') </script>'
    webform_table:
      '#type': table
      '#header':
        - 'Header 1'
        - 'Header 2'
      '#rows':
        -
          - 'Row 1 - Col 1'
          - 'Row 1 - Col 2'
        -
          - 'Row 2 - Col 1'
          - 'Row 2 - Col 2'
        -
          - 'Row 3 - Col 1'
          - 'Row 3 - Col 2'
  date_time_elements:
    '#type': details
    '#title': 'Date/time elements | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    date:
      '#type': date
      '#title': 'Date | <script>alert(''This markup is not escaped properly!!!'') </script>'
    date_multiple:
      '#type': date
      '#title': 'Date multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    datetime:
      '#type': datetime
      '#title': 'Date/time | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2419131">Issue #2419131: #states attribute does not work on #type datetime</a>'
    datetime_multiple:
      '#type': datetime
      '#title': 'Date/time multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    datelist:
      '#type': datelist
      '#title': 'Date list | <script>alert(''This markup is not escaped properly!!!'') </script>'
    datelist_multiple:
      '#type': datelist
      '#title': 'Date list multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
    webform_time:
      '#type': webform_time
      '#title': 'Time | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/1838234">Issue #1838234: Add jQuery Timepicker for the Time element of the datetime field</a>'
    webform_time_multiple:
      '#type': webform_time
      '#title': 'Time multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#multiple': true
  entity_reference_elements:
    '#type': details
    '#title': 'Entity reference elements | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    entity_autocomplete:
      '#type': entity_autocomplete
      '#title': 'Entity autocomplete | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#target_type': user
      '#selection_handler': 'default:user'
      '#selection_settings':
        include_anonymous: true
      '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2826451">Issue #2826451: TermSelection returning HTML characters in select list</a>'
    entity_autocomplete_tags:
      '#type': entity_autocomplete
      '#title': 'Entity autocomplete tags | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#target_type': user
      '#selection_handler': 'default:user'
      '#selection_settings':
        include_anonymous: true
      '#tags': true
    entity_autocomplete_multiple:
      '#type': entity_autocomplete
      '#title': 'Entity autocomplete multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#target_type': user
      '#selection_handler': 'default:user'
      '#selection_settings':
        include_anonymous: true
      '#multiple': true
    webform_entity_checkboxes:
      '#type': webform_entity_checkboxes
      '#title': 'Entity checkboxes | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options_display': side_by_side
      '#target_type': user
      '#selection_handler': 'default:user'
      '#selection_settings':
        include_anonymous: true
      '#options':
        1: Administrator
        0: Anonymous
    webform_entity_radios:
      '#type': webform_entity_radios
      '#title': 'Entity radios | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#options_display': side_by_side
      '#target_type': user
      '#selection_handler': 'default:user'
      '#selection_settings':
        include_anonymous: true
      '#options':
        1: Administrator
        0: Anonymous
    webform_entity_select:
      '#type': webform_entity_select
      '#title': 'Entity select | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#target_type': user
      '#selection_handler': 'default:user'
      '#selection_settings':
        include_anonymous: true
      '#options':
        1: Administrator
        0: Anonymous
    webform_entity_select_multiple:
      '#type': webform_entity_select
      '#title': 'Entity select multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#target_type': user
      '#selection_handler': 'default:user'
      '#selection_settings':
        include_anonymous: true
      '#options':
        1: Administrator
        0: Anonymous
      '#multiple': true
      '#select2': true
    webform_term_checkboxes:
      '#type': webform_term_checkboxes
      '#title': 'Term checkboxes | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#vocabulary': tags
    webform_term_select:
      '#type': webform_term_select
      '#title': 'Term select | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#vocabulary': tags
    webform_term_select_multiple:
      '#type': webform_term_select
      '#title': 'Term select multiple | <script>alert(''This markup is not escaped properly!!!'') </script>'
      '#vocabulary': tags
      '#multiple': true
      '#select2': true
  other_elements:
    '#type': details
    '#title': 'Other elements | <script>alert(''This markup is not escaped properly!!!'') </script>'
    '#open': true
    language_select:
      '#type': language_select
      '#title': 'Language select | <script>alert(''This markup is not escaped properly!!!'') </script>'
    machine_name:
      '#type': machine_name
      '#title': 'Machine name | <script>alert(''This markup is not escaped properly!!!'') </script>'
    table:
      '#type': table
      '#title': 'Table | <script>alert(''This markup is not escaped properly!!!'') </script>'
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
