uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_element_computed_twig
title: 'Test: Element: Computed twig'
description: 'Test computed twig element.'
categories:
  - 'Test: Element'
elements: |
  number:
    '#type': number
    '#title': number
    '#default_value': 2
  simple_string:
    '#type': textfield
    '#title': simple_string
    '#default_value': 'This is a string'
  complex_string:
    '#type': textfield
    '#title': complex_string
    '#default_value': 'This is a <strong>complex</strong> string, which contains "double" and ''single'' quotes with special characters like >, <, ><, and <>.'
  text_format:
    '#type': text_format
    '#title': text_format
    '#default_value': |
      <p>This is a <strong>text format</strong> string.</p>
      <p>It contains &quot;double&quot; and 'single' quotes with special characters like &lt;, &gt;, &lt;&gt;, and &gt;&lt;.</p>

  xss:
    '#type': textfield
    '#title': xss
    '#default_value': '<script>alert("XSS");</script>'
  custom_attributes:
    '#type': webform_computed_twig
    '#title': custom_attributes
    '#wrapper_attributes':
      class:
        - wrapper-custom
      style: 'border: 1px solid red'
    '#attributes':
      class:
        - element-custom
      style: 'border: 1px solid yellow'
    '#label_attributes':
      class:
        - label-custom
      style: 'border: 1px solid blue'
    '#template': 'Testing attributes'
  webform_computed_twig_auto:
    '#type': webform_computed_twig
    '#title': webform_computed_twig_auto
    '#template': |
      <b class="webform_computed_twig_auto">number:</b> {{ (webform_token('[webform_submission:values:number:clear]', webform_submission) ?: 0) }} * 2 = {{ (webform_token('[webform_submission:values:number:clear]', webform_submission) ?: 0) * 2 }}<br />
      <b class="webform_computed_twig_auto">simple string:</b> {{ webform_token('[webform_submission:values:simple_string]', webform_submission) }}<br />
      <b class="webform_computed_twig_auto">complex string:</b> {{ webform_token('[webform_submission:values:complex_string]', webform_submission) }}<br />
      <b class="webform_computed_twig_auto">text_format:</b> {{ webform_token('[webform_submission:values:text_format]', webform_submission) }}<br />
      <b class="webform_computed_twig_auto">xss:</b> {{ webform_token('[webform_submission:values:xss]', webform_submission) }}<br />

  webform_computed_twig_html:
    '#type': webform_computed_twig
    '#title': webform_computed_twig_html
    '#mode': html
    '#template': |
      <b class="webform_computed_twig_html">number:</b> {{ (webform_token('[webform_submission:values:number:clear]', webform_submission) ?: 0) }} * 2 = {{ (webform_token('[webform_submission:values:number:clear]', webform_submission) ?: 0) * 2 }}<br />
      <b class="webform_computed_twig_html">simple string:</b> {{ webform_token('[webform_submission:values:simple_string]', webform_submission) }}<br />
      <b class="webform_computed_twig_html">complex string:</b> {{ webform_token('[webform_submission:values:complex_string]', webform_submission) }}<br />
      <b class="webform_computed_twig_html">text_format:</b> {{ webform_token('[webform_submission:values:text_format]', webform_submission) }}<br />
      <b class="webform_computed_twig_html">xss:</b> {{ webform_token('[webform_submission:values:xss]', webform_submission) }}<br />

  webform_computed_twig_text:
    '#type': webform_computed_twig
    '#title': webform_computed_twig_text
    '#mode': text
    '#template': |
      number: {{ (webform_token('[webform_submission:values:number:clear]', webform_submission) ?: 0) }} * 2 = {{ (webform_token('[webform_submission:values:number:clear]', webform_submission) ?: 0) * 2 }}
      simple string: {{ webform_token('[webform_submission:values:simple_string]', webform_submission) }}
      complex string: {{ webform_token('[webform_submission:values:complex_string]', webform_submission) }}
      text_format: {{ webform_token('[webform_submission:values:text_format]', webform_submission) }}
      xss: {{ webform_token('[webform_submission:values:xss]', webform_submission) }}

  webform_computed_twig_data:
    '#type': webform_computed_twig
    '#title': webform_computed_twig_data
    '#template': |
      <b class="webform_computed_twig_data">number:</b> {{ data.number }} * 2 = {{ data.number * 2 }}<br />
      <b class="webform_computed_twig_data">simple string:</b> {{ data.simple_string }}<br />
      <b class="webform_computed_twig_data">complex string:</b> {{ data.complex_string }}<br />
      <b class="webform_computed_twig_data">text_format:</b> {{ data.text_format.value }}<br />
      <b class="webform_computed_twig_data">xss:</b> {{ data.xss }}<br />

  webform_computed_twig_store:
    '#type': webform_computed_twig
    '#title': webform_computed_twig_store
    '#mode': text
    '#display_on': none
    '#store': true
    '#template': 'sid: {{ data.sid }}'
  webform_computed_twig_trim:
    '#type': webform_computed_twig
    '#title': webform_computed_twig_trim
    '#whitespace': trim
    '#template': ' <em>This is trimmed</em>  <br/> '
  webform_computed_twig_spaceless:
    '#type': webform_computed_twig
    '#title': webform_computed_twig_spaceless
    '#whitespace': spaceless
    '#template': ' <em>This is spaceless</em>  <br/> '
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 1
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: message
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
