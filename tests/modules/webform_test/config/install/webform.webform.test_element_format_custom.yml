uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_element_format_custom
title: 'Test: Element: Format custom'
description: 'Test element custom formatting.'
categories:
  - 'Test: Element'
elements: |
  textfield_custom_value:
    '#type': textfield
    '#title': textfield_custom
    '#format': custom
    '#format_html': |
      <em>{{ value }}</em>

    '#format_text': |
      /{{ value }}/

    '#default_value': '{textfield_custom}'
  textfield_custom_token:
    '#type': textfield
    '#title': textfield_custom_token
    '#format': custom
    '#format_html': |
      <em>[webform_submission:values:textfield_custom_token:raw]</em>

    '#format_text': |
      /[webform_submission:values:textfield_custom_token:raw]/

    '#default_value': '{textfield_custom_token}'
  textfield_custom_token_exception:
    '#type': textfield
    '#title': textfield_custom_token_exception
    '#format': custom
    '#format_html': |
      <em>EXCEPTION[webform_submission:values:textfield_custom_token_exception]</em>

    '#format_text': |
      /EXCEPTION[webform_submission:values:textfield_custom_token_exception]/

    '#default_value': '{textfield_custom_token_exception}'
  textfield_custom_multiple:
    '#type': textfield
    '#title': textfield_custom_value
    '#multiple': true
    '#format': custom
    '#format_html': |
      <em>{{ value }}</em>
    '#format_text': |
      /{{ value }}/
    '#default_value':
      - One
      - Two
      - Three
      - Four
      - Five
  textfield_custom_value_multiple:
    '#type': textfield
    '#title': textfield_custom_value_multiple
    '#multiple': true
    '#format': custom
    '#format_html': |
      <em>{{ value }}</em>

    '#format_text': |
      /{{ value }}/

    '#format_items': custom
    '#format_items_html': |
      <table>
      {% for item in items %}
        <tr {% if loop.index is divisible by(2) %}style="background-color: #ffc"{% endif %}><td>{{ item }}</td></tr>
      {% endfor %}
      </table>

    '#format_items_text': |
      {% for item in items %}
      ⦿ {{ item }}
      {% endfor %}

    '#default_value':
      - One
      - Two
      - Three
      - Four
      - Five
  image_custom:
    '#type': webform_image_file
    '#title': image_custom
    '#format': custom
    '#format_html': |
      value: {{ value }}<br/>
      item['value']: {{ item['value'] }}<br/>
      item['raw']: {{ item['raw'] }}<br/>
      item['link']: {{ item['link'] }}<br/>
      item['id']: {{ item['id'] }}<br/>
      item['url']: {{ item['url'] }}<br/>
      item['original:image']: <div style="width: 100px">{{ item['original:image'] }}</div>
      item['original:link']: <div style="width: 100px">{{ item['original:link'] }}</div>
      item['original:modal']: <div style="width: 100px">{{ item['original:modal'] }}</div>

    '#format_text': |
      value: {{ value }}
      item['value']: {{ item['value'] }}
      item['raw']: {{ item['raw'] }}
      item['link']: {{ item['link'] }}
      item['id']: {{ item['id'] }}
      item['url']: {{ item['url'] }}

  address_custom:
    '#type': webform_address
    '#title': address_custom
    '#format': custom
    '#format_html': |
      element.address: {{ element.address }}<br/>
      element.address_2: {{ element.address_2 }}<br/>
      element.city: {{ element.city }}<br/>
      element.state_province: {{ element.state_province }}<br/>
      element.postal_code: {{ element.postal_code }}<br/>
      element.country: {{ element.country }}<br/>

    '#format_text': |
      element.address: {{ element.address }}
      element.address_2: {{ element.address_2 }}
      element.city: {{ element.city }}
      element.state_province: {{ element.state_province }}
      element.postal_code: {{ element.postal_code }}
      element.country: {{ element.country }}

    '#state_province__type': webform_select_other
    '#country__type': webform_select_other
    '#default_value':
      address: '{address}'
      address_2: '{address_2}'
      city: '{city}'
      state_province: '{state_province}'
      postal_code: '{postal_code}'
      country: '{country}'
  address_multiple_custom:
    '#type': webform_address
    '#title': address_multiple_custom
    '#multiple': true
    '#default_value':
      - address: '{01-address}'
        address_2: '{01-address_2}'
        city: '{01-city}'
        state_province: '{01-state_province}'
        postal_code: '{01-postal_code}'
        country: '{01-country}'
      - address: '{02-address}'
        address_2: '{02-address_2}'
        city: '{02-city}'
        state_province: '{02-state_province}'
        postal_code: '{02-postal_code}'
        country: '{02-country}'
    '#format': custom
    '#format_html': |
      element.address: {{ element.address }}<br/>
      element.address_2: {{ element.address_2 }}<br/>
      element.city: {{ element.city }}<br/>
      element.state_province: {{ element.state_province }}<br/>
      element.postal_code: {{ element.postal_code }}<br/>
      element.country: {{ element.country }}<br/>

    '#format_text': |
      element.address: {{ element.address }}
      element.address_2: {{ element.address_2 }}
      element.city: {{ element.city }}
      element.state_province: {{ element.state_province }}
      element.postal_code: {{ element.postal_code }}
      element.country: {{ element.country }}

    '#format_items': custom
    '#format_items_html': |
      {% for item in items %}
      <div>*****</div>
      {{ item }}
      <div>*****</div>
      {% endfor %}

    '#format_items_text': |
      {% for item in items %}
      *****
      {{ item }}
      *****
      {% endfor %}

    '#state_province__type': webform_select_other
    '#country__type': webform_select_other
  fieldset_custom:
    '#type': fieldset
    '#title': fieldset_custom
    '#format': custom
    '#format_html': |
      {{ item.details }}

    '#format_text': |
      {{ item.details }}

    fieldset_custom_textfield:
      '#type': textfield
      '#title': fieldset_custom_textfield
      '#default_value': '{fieldset_custom_textfield}'
  fieldset_custom_children:
    '#type': fieldset
    '#title': fieldset_custom_children
    '#format': custom
    '#format_html': |
      <h3>fieldset_custom_children</h3>
      <hr />
      {{ children }}

    '#format_text': |
      fieldset_custom_children
      ------------------------
      {{ children }}

    fieldset_custom_children_textfield:
      '#type': textfield
      '#title': fieldset_custom_children_textfield
      '#default_value': '{fieldset_custom_children_textfield}'
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 1
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
