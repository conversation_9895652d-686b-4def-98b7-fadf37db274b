uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_element_table
title: 'Test: Element: Table'
description: 'Test table elements.'
categories:
  - 'Test: Element'
elements: |
  table:
    '#type': table
    '#header':
      - 'First Name'
      - 'Last Name'
      - Gender
      - Markup
    table__1:
      table__1__first_name:
        '#type': textfield
        '#size': 20
        '#default_value': <PERSON>
      table__1__last_name:
        '#type': textfield
        '#size': 20
        '#default_value': Smith
      table__1__gender:
        '#type': select
        '#options': gender
        '#default_value': Man
      table__1__markup:
        '#markup': '{markup_1}'
    table__2:
      table__2__first_name:
        '#type': textfield
        '#size': 20
        '#default_value': Jane
      table__2__last_name:
        '#type': textfield
        '#size': 20
        '#default_value': Doe
      table__2__gender:
        '#type': select
        '#options': gender
        '#default_value': Woman
      table__2__markup:
        '#markup': '{markup_2}'
  table_basic:
    '#type': webform_table
    '#title': table_basic
    '#header':
      - title: 'First Name'
        attributes: {  }
      - title: 'Last Name'
        attributes: {  }
      - title: Gender
        attributes: {  }
      - title: Markup
    table_basic_01:
      '#type': webform_table_row
      '#title': 'Basic Person (1)'
      table_basic_01_first_name:
        '#type': textfield
        '#title': 'First name (1)'
        '#title_display': invisible
      table_basic_01_last_name:
        '#type': textfield
        '#title': 'Last name (1)'
        '#title_display': invisible
      table_basic_01_gender:
        '#type': select
        '#title': 'Gender (1)'
        '#title_display': invisible
        '#options': gender
      table_basic_01_markup:
        '#markup': '{markup_1}'
  table_advanced:
    '#type': webform_table
    '#title': table_advanced
    '#title_display': invisible
    '#format': details
    '#header':
      - title: Composite
        attributes:
          width: 50%
      - title: Nested
        attributes:
          width: 50%
    '#sticky': true
    '#caption': '{caption}'
    table_advanced_01:
      '#type': webform_table_row
      '#title': 'Advanced Person (1)'
      table_advanced_01_address:
        '#type': webform_address
        '#title': Address
        '#admin_title': 'Address (1)'
      table_advanced_01_container:
        '#type': container
        table_advanced_01_first_name:
          '#type': textfield
          '#title': 'First name'
          '#admin_title': 'First name (1)'
        table_advanced_01_last_name:
          '#type': textfield
          '#title': 'Last name'
          '#admin_title': 'Last name (1)'
        table_advanced_01_gender:
          '#type': select
          '#title': Gender
          '#options': gender
          '#admin_title': 'Gender (2)'
        table_advanced_01_managed_file:
          '#type': managed_file
          '#title': Files
          '#admin_title': 'Files (2)'
          '#multiple': true
  table_prefix_children_false:
    '#type': webform_table
    '#title': table_prefix_children_false
    '#prefix_children': false
    table_prefix_children_false_01:
      '#type': webform_table_row
      '#title': table_prefix_children_false_01
  table_rows:
    '#type': select
    '#title': table_rows
    '#options':
      1: '1'
      2: '2'
      3: '3'
      4: '4'
    '#default_value': '1'
  table_states:
    '#type': webform_table
    '#title': table_states
    '#states':
      invisible:
        ':input[name="table_rows"]':
          value: ''
    '#header':
      - title: Item
        attributes: {  }
    table_states_01:
      '#type': webform_table_row
      '#title': 'item (1)'
      '#states':
        visible:
          ':input[name="table_rows"]':
            value:
              greater: '0'
      table_advanced_01_textfield:
        '#type': textfield
        '#title': 'textfield (1)'
    table_states_02:
      '#type': webform_table_row
      '#title': 'item (2)'
      '#states':
        visible:
          ':input[name="table_rows"]':
            value:
              greater: '1'
      table_advanced_02_textfield:
        '#type': textfield
        '#title': 'textfield (2)'
    table_states_03:
      '#type': webform_table_row
      '#title': 'item (3)'
      '#states':
        visible:
          ':input[name="table_rows"]':
            value:
              greater: '2'
      table_advanced_03_textfield:
        '#type': textfield
        '#title': 'textfield (3)'
    table_states_04:
      '#type': webform_table_row
      '#title': 'item (4)'
      '#states':
        visible:
          ':input[name="table_rows"]':
            value:
              greater: '3'
      table_advanced_04_textfield:
        '#type': textfield
        '#title': 'textfield (4)'
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 1
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: message
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  debug:
    id: debug
    label: Debug
    notes: ''
    handler_id: debug
    status: true
    conditions: {  }
    weight: 0
    settings:
      format: yaml
      submission: false
variants: {  }
