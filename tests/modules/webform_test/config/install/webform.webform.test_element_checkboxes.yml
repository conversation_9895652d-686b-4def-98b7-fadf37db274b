uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_element_checkboxes
title: 'Test: Element: Checkboxes'
description: 'Test the checkboxes.'
categories:
  - 'Test: Element'
elements: |
  checkbox_example:
    '#type': details
    '#title': Checkbox
    '#open': true
    checkbox:
      '#type': checkbox
      '#title': checkbox
    checkbox_exclude_empty:
      '#type': checkbox
      '#title': checkbox_exclude_empty
      '#exclude_empty': true
  checkboxes_descriptions_example:
    '#type': details
    '#title': 'Checkboxes descriptions'
    '#open': true
    checkboxes_description:
      '#type': checkboxes
      '#title': checkboxes_description
      '#options_description_display': description
      '#options':
        one: 'One -- This is a description'
        two: 'Two -- This is a description'
        three: 'Three -- This is a description'
    checkboxes_help:
      '#type': checkboxes
      '#title': checkboxes_help
      '#options_description_display': help
      '#options':
        one: 'One -- This is a description'
        two: 'Two -- This is a description'
        three: 'Three -- This is a description'
  checkboxes_inline_title_example:
    '#type': details
    '#title': 'Checkboxes inline title'
    '#open': true
    checkboxes_inline_title:
      '#type': checkboxes
      '#title': checkboxes_inline_title
      '#title_display': inline
      '#options': yes_no
      '#options_display': side_by_side
    checkboxes_other_inline_title:
      '#type': webform_checkboxes_other
      '#title': checkboxes_other_inline_title
      '#title_display': inline
      '#options': yes_no
      '#options_display': side_by_side
  checkboxes_required_conditional_example:
    '#type': details
    '#title': 'Checkboxes required conditional'
    '#open': true
    checkboxes_required_conditional_trigger:
      '#type': checkbox
      '#title': 'Require the below checkboxes'
      '#default_value': true
    checkboxes_required_conditions:
      '#type': checkboxes
      '#title': checkboxes_required
      '#options': yes_no
      '#states':
        required:
          ':input[name="checkboxes_required_conditional_trigger"]':
            checked: true
    checkboxes_other_required_conditions:
      '#type': webform_checkboxes_other
      '#title': checkboxes_other_required
      '#options': yes_no
      '#states':
        required:
          ':input[name="checkboxes_required_conditional_trigger"]':
            checked: true
  checkboxes_buttons_example:
    '#type': details
    '#title': 'checkboxes CSS buttons'
    '#open': true
    '#required': true
    checkboxes_buttons:
      '#type': checkboxes
      '#title': checkboxes_buttons
      '#options': yes_no
      '#options_display': buttons
    checkboxes_buttons_horizontal:
      '#type': checkboxes
      '#title': checkboxes_buttons_horizontal
      '#options_display': buttons_horizontal
      '#options_description_display': description
      '#options':
        one: One
        two: Two
        three: 'Three -- This is a description'
    checkboxes_buttons_vertical:
      '#type': checkboxes
      '#title': checkboxes_buttons_vertical
      '#options': yes_no
      '#options_display': buttons_vertical
    checkboxes_buttons_inline:
      '#type': checkboxes
      '#title': checkboxes_buttons_inline
      '#title_display': inline
      '#options': yes_no
      '#options_display': buttons
    checkboxes_buttons_description:
      '#type': checkboxes
      '#title': checkboxes_buttons_description
      '#options_display': buttons
      '#options_description_display': description
      '#options':
        one: 'One -- This is a description'
        two: 'Two -- This is a description'
        three: 'Three -- This is a description'
    checkboxes_buttons_help:
      '#type': checkboxes
      '#title': checkboxes_buttons_help
      '#options_display': buttons
      '#options_description_display': help
      '#options':
        one: 'One -- This is a description'
        two: 'Two -- This is a description'
        three: 'Three -- This is a description'
    checkboxes_buttons_wrapping:
      '#type': checkboxes
      '#title': checkboxes_buttons_wrapping
      '#options':
        one: OneOneOneOneOneOneOne
        two: Two
        three: Three
        four: Four
        five: Five
        six: 'Six Six Six Six Six Six'
      '#options_display': buttons
  checkboxes_options_properties_example:
    '#type': details
    '#title': 'checkboxes custom options properties'
    '#open': true
    checkboxes_options_properties:
      '#type': checkboxes
      '#title': checkboxes_options_properties
      '#options_description_display': description
      '#options':
        one: 'One -- This is a description'
        two: 'Two -- This is a description'
        three: 'Three -- This is a description'
      '#options__properties':
        two:
          '#wrapper_attributes':
            data-custom: 'custom wrapper data'
            style: 'border: red 1px solid'
            class:
              - one-custom-wrapper-class
          '#label_attributes':
            data-custom: 'custom label data'
            style: 'border: blue 1px solid'
            class:
              - one-custom-label-class
          '#attributes':
            data-custom: 'custom input data'
            style: 'border: yellow 1px solid'
            class:
              - one-custom-class
        three:
          '#disabled': true
    checkboxes_buttons_options_properties:
      '#type': checkboxes
      '#title': checkboxes_buttons_options_properties
      '#options_display': buttons
      '#options_description_display': description
      '#options':
        one: 'One -- This is a description'
        two: 'Two -- This is a description'
        three: 'Three -- This is a description'
      '#options__properties':
        one:
          '#label_attributes':
            style: 'background-color: #ffcccb; color: darkred'
        two:
          '#label_attributes':
            style: 'background-color: lightblue; color: darkblue'
        three:
          '#disabled': true
    checkboxes_other_options_properties:
      '#type': webform_checkboxes_other
      '#title': checkboxes_other_options_properties
      '#options':
        one: One
        two: Two
        three: Three
      '#options__properties':
        one:
          '#disabled': true
        two:
          '#disabled': true
        three:
          '#disabled': true
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 1
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: message
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  debug:
    id: debug
    label: Debug
    notes: ''
    handler_id: debug
    status: true
    conditions: {  }
    weight: 1
    settings:
      format: yaml
      submission: false
variants: {  }
