uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_states_autocomplete
title: 'Test: Form API #states autocomplete'
description: 'Test Drupal''s #states autocomplete.'
categories:
  - 'Test: Form API #states'
elements: |
  select:
    '#type': select
    '#title': select
    '#options':
      One: One
      Two: Two
      Three: Three
  select_multiple:
    '#type': select
    '#title': select_multiple
    '#multiple': true
    '#options':
      One: One
      Two: Two
      Three: Three
  webform_select_other:
    '#type': webform_select_other
    '#title': webform_select_other
    '#options':
      One: One
      Two: Two
      Three: Three
  radios:
    '#type': radios
    '#title': radios
    '#options':
      One: One
      Two: Two
      Three: Three
  webform_radios_other:
    '#type': webform_radios_other
    '#title': webform_radios_other
    '#options':
      One: One
      Two: Two
      Three: Three
  checkboxes:
    '#type': checkboxes
    '#title': checkboxes
    '#options':
      One: One
      Two: Two
      Three: Three
  webform_checkboxes_other:
    '#type': webform_checkboxes_other
    '#title': webform_checkboxes_other
    '#options':
      One: One
      Two: Two
      Three: Three
  webform_buttons:
    '#type': webform_buttons
    '#title': webform_buttons
    '#options':
      One: One
      Two: Two
      Three: Three
  webform_buttons_other:
    '#type': webform_buttons_other
    '#title': webform_buttons_other
    '#options':
      One: One
      Two: Two
      Three: Three
  tableselect_single:
    '#type': tableselect
    '#title': tableselect_single
    '#multiple': false
    '#options':
      One: One
      Two: Two
      Three: Three
  tableselect_multiple:
    '#type': tableselect
    '#title': tableselect_multiple
    '#options':
      One: One
      Two: Two
      Three: Three
  webform_entity_select:
    '#type': webform_entity_select
    '#title': webform_entity_select
    '#target_type': user
    '#selection_handler': 'default:user'
    '#selection_settings':
      include_anonymous: true
  webform_likert:
    '#type': webform_likert
    '#title': webform_likert
    '#questions':
      q1: 'Question 1'
      q2: 'Question 2'
      q3: 'Question 3'
    '#answers':
      1: 'Option 1'
      2: 'Option 2'
      3: 'Option 3'
  webform_address:
    '#type': webform_address
    '#title': webform_address
    '#state_province__type': webform_select_other
  webform_custom_composite:
    '#type': webform_custom_composite
    '#title': webform_custom_composite
    '#element':
      textfield:
        '#type': textfield
        '#title': textfield
      select:
        '#type': select
        '#title': select
        '#options':
          One: One
          Two: Two
          Three: Three
  webform_element_composite:
    '#type': webform_element_composite
    '#title': webform_element_composite
    '#default_value':
      first_name:
        '#type': textfield
        '#title': 'First name'
      last_name:
        '#type': textfield
        '#title': 'Last name'
      sex:
        '#type': select
        '#options':
          Male: Male
          Female: Female
        '#title': Sex
  webform_image_select:
    '#type': webform_image_select
    '#title': webform_image_select
    '#images':
      kitten_1:
        text: 'Cute Kitten 1'
        src: 'http://placekitten.com/220/200'
      kitten_2:
        text: 'Cute Kitten 2'
        src: 'http://placekitten.com/180/200'
      kitten_3:
        text: 'Cute Kitten 3'
        src: 'http://placekitten.com/130/200'
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: true
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: message
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: true
  results_disabled_ignore: true
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
