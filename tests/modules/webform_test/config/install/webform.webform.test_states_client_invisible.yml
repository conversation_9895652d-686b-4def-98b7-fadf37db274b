uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_states_client_invisible
title: 'Test: Form API #states invisible and not required'
description: 'Test Drupal''s #states API invisible states.'
categories:
  - 'Test: Form API #states'
elements: |
  trigger:
    '#type': checkbox
    '#title': 'Hide and empty elements'
  container:
    '#type': container
    '#title': Container
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  details:
    '#type': details
    '#title': Details
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2348851">Issue #2348851: Regression: Allow HTML tags inside detail summary</a>'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  fieldset:
    '#type': fieldset
    '#title': Fieldset
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_section:
    '#type': webform_section
    '#title': Section
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_address:
    '#type': webform_address
    '#title': 'Basic address'
    '#default_value':
      address: '10 Main Street'
      address_2: '10 Main Street'
      city: Springfield
      state_province: Alabama
      postal_code: '11111'
      country: Afghanistan
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  address:
    '#type': address
    '#title': 'Advanced address'
    '#default_value':
      given_name: John
      family_name: Smith
      organization: 'Google Inc.'
      address_line1: '1098 Alta Ave'
      postal_code: '94043'
      locality: 'Mountain View'
      administrative_area: CA
      country_code: US
      langcode: en
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  processed_text:
    '#type': processed_text
    '#title': 'Advanced HTML/Text'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_entity_print_attachment_pdf:
    '#type': webform_entity_print_attachment
    '#title': 'Attachment PDF'
    '#default_value': Loremipsum
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_attachment_token:
    '#type': webform_attachment_token
    '#title': 'Attachment token'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_attachment_twig:
    '#type': webform_attachment_twig
    '#title': 'Attachment Twig'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_attachment_url:
    '#type': webform_attachment_url
    '#title': 'Attachment URL'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_audio_file:
    '#type': webform_audio_file
    '#title': 'Audio file'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_autocomplete:
    '#type': webform_autocomplete
    '#title': Autocomplete
    '#default_value': Loremipsum
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_markup:
    '#type': webform_markup
    '#title': 'Basic HTML'
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2700667">Issue #2700667: Notice: Undefined index: #type in drupal_process_states()</a>'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  checkbox:
    '#type': checkbox
    '#title': Checkbox
    '#default_value': true
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  checkboxes:
    '#type': checkboxes
    '#title': Checkboxes
    '#options':
      one: One
      two: Two
      three: Three
    '#options_display': side_by_side
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/994360">Issue #994360: #states cannot disable/enable radios and checkboxes</a><br /><a href="https://www.drupal.org/node/2836364">Issue #2836364: Wrapper attributes are not supported by composite elements, this includes radios, checkboxes, and buttons.</a>'
    '#default_value':
      - one
      - two
      - three
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_checkboxes_other:
    '#type': webform_checkboxes_other
    '#title': 'Checkboxes other'
    '#options':
      one: One
      two: Two
      three: Three
    '#options_display': side_by_side
    '#default_value':
      - one
      - two
      - three
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_codemirror:
    '#type': webform_codemirror
    '#title': CodeMirror
    '#mode': yaml
    '#default_value': 'message: ''Hello World'''
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  color:
    '#type': color
    '#title': Color
    '#default_value': '#ffffcc'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_computed_token:
    '#type': webform_computed_token
    '#title': 'Computed token'
    '#template': 'This is a Computed token value.'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_computed_twig:
    '#type': webform_computed_twig
    '#title': 'Computed Twig'
    '#template': 'This is a Computed Twig value.'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_contact:
    '#type': webform_contact
    '#title': Contact
    '#default_value':
      name: Loremipsum
      company: Loremipsum
      email: <EMAIL>
      phone: ************
      address: '10 Main Street'
      address_2: '10 Main Street'
      city: Springfield
      state_province: Alabama
      postal_code: '11111'
      country: Afghanistan
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_custom_composite:
    '#type': webform_custom_composite
    '#title': 'Custom composite'
    '#element':
      name:
        '#type': textfield
        '#title': Name
        '#title_display': invisible
      gender:
        '#type': select
        '#title': Gender
        '#title_display': invisible
        '#options':
          Male: Male
          Female: Female
    '#default_value':
      - name: Loremipsum
        gender: Male
      - name: Loremipsum
        gender: Male
      - name: Loremipsum
        gender: Male
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  date:
    '#type': date
    '#title': Date
    '#default_value': '2022-11-06T09:35:54-0500'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  datetime:
    '#type': datetime
    '#title': Date/time
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2419131">Issue #2419131: #states attribute does not work on #type datetime</a>'
    '#default_value': '2035-11-08T21:25:29-0500'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  datelist:
    '#type': datelist
    '#title': 'Date list'
    '#default_value': '2020-01-15T09:55:30-0500'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_document_file:
    '#type': webform_document_file
    '#title': 'Document file'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  email:
    '#type': email
    '#title': Email
    '#default_value': <EMAIL>
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_email_confirm:
    '#type': webform_email_confirm
    '#title': 'Email confirm'
    '#default_value': <EMAIL>
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_email_multiple:
    '#type': webform_email_multiple
    '#title': 'Email multiple'
    '#default_value': '<EMAIL>, <EMAIL>, <EMAIL>'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  entity_autocomplete:
    '#type': entity_autocomplete
    '#title': 'Entity autocomplete'
    '#target_type': user
    '#selection_handler': 'default:user'
    '#selection_settings':
      include_anonymous: true
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2826451">Issue #2826451: TermSelection returning HTML characters in select list</a>'
    '#default_value': 6
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_entity_checkboxes:
    '#type': webform_entity_checkboxes
    '#title': 'Entity checkboxes'
    '#options_display': side_by_side
    '#target_type': user
    '#selection_handler': 'default:user'
    '#selection_settings':
      include_anonymous: true
    '#options':
      1: Administrator
      0: Anonymous
    '#default_value':
      - 1
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_entity_radios:
    '#type': webform_entity_radios
    '#title': 'Entity radios'
    '#options_display': side_by_side
    '#target_type': user
    '#selection_handler': 'default:user'
    '#selection_settings':
      include_anonymous: true
    '#options':
      1: Administrator
      0: Anonymous
    '#default_value': 1
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_entity_select:
    '#type': webform_entity_select
    '#title': 'Entity select'
    '#target_type': user
    '#selection_handler': 'default:user'
    '#selection_settings':
      include_anonymous: true
    '#options':
      1: Administrator
      0: Anonymous
    '#default_value': 1
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  managed_file:
    '#type': managed_file
    '#title': File
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2705471">Issue #2705471: Webform states managed file fields</a><br /><a href="https://www.drupal.org/node/2113931">Issue #2113931: File Field design update</a><br /><a href="https://www.drupal.org/node/2346893">Issue #2346893: Duplicate Ajax wrapper around a file field</a><br /><a href="https://www.drupal.org/node/2482783">Issue #2482783: File upload errors not set or shown correctly</a>'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_height:
    '#type': webform_height
    '#title': 'Height (feet/inches)'
    '#default_value': 47
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_horizontal_rule:
    '#type': webform_horizontal_rule
    '#attributes':
      class:
        - webform-horizontal-rule--dotted
        - webform-horizontal-rule--thick
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_image_file:
    '#type': webform_image_file
    '#title': 'Image file'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_image_select:
    '#type': webform_image_select
    '#title': 'Image select'
    '#show_label': true
    '#images':
      dog_1:
        text: 'Dog 1'
        src: 'https://www.placedog.net/80/100'
      dog_2:
        text: 'Dog 2'
        src: 'https://www.placedog.net/100/100'
      dog_3:
        text: 'Dog 3'
        src: 'https://www.placedog.net/120/100'
    '#default_value': Loremipsum
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  item:
    '#type': item
    '#title': Item
    '#markup': '{markup}'
    '#field_prefix': '{field_prefix}'
    '#field_suffix': '{field_suffix}'
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/783438">Issue #783438: #states doesn''t work for #type item</a>'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  label:
    '#type': label
    '#title': Label
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  language_select:
    '#type': language_select
    '#title': 'Language select'
    '#default_value': en
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_likert:
    '#type': webform_likert
    '#title': Likert
    '#questions':
      q1: 'Please answer question 1?'
      q2: 'How about now answering question 2?'
      q3: 'Finally, here is question 3?'
    '#answers':
      1: '1'
      2: '2'
      3: '3'
    '#default_value':
      q1: 1
      q2: 1
      q3: 1
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_link:
    '#type': webform_link
    '#title': Link
    '#default_value':
      title: Loremipsum
      url: 'http://example.com'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_mapping:
    '#type': webform_mapping
    '#title': Mapping
    '#source':
      one: One
      two: Two
      three: Three
    '#destination':
      four: Four
      five: Five
      six: Six
    '#default_value':
      one: four
      two: four
      three: four
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_message:
    '#type': webform_message
    '#title': Message
    '#message_type': warning
    '#message_message': 'This is a <strong>warning</strong> message.'
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/77245">Issue #77245: A place for JavaScript status messages</a>'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_name:
    '#type': webform_name
    '#title': Name
    '#default_value':
      title: Miss
      first: Loremipsum
      middle: Loremipsum
      last: Loremipsum
      suffix: Loremipsum
      degree: Loremipsum
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  number:
    '#type': number
    '#title': Number
    '#min': 0
    '#max': 10
    '#step': 1
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  password:
    '#type': password
    '#title': Password
    '#default_value': Loremipsum
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  password_confirm:
    '#type': password_confirm
    '#title': 'Password confirm'
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/1427838">Issue #1427838: password and password_confirm children do not pick up #states or #attributes</a>'
    '#default_value': Loremipsum
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  radios:
    '#type': radios
    '#title': Radios
    '#options':
      one: One
      two: Two
      three: Three
    '#options_display': side_by_side
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/2731991">Issue #2731991: Setting required on radios marks all options required</a><br /><a href="https://www.drupal.org/node/994360">Issue #994360: #states cannot disable/enable radios and checkboxes</a><br /><a href="https://www.drupal.org/node/2836364">Issue #2836364: Wrapper attributes are not supported by composite elements, this includes radios, checkboxes, and buttons.</a>'
    '#default_value': one
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_radios_other:
    '#type': webform_radios_other
    '#title': 'Radios other'
    '#options':
      one: One
      two: Two
      three: Three
    '#options_display': side_by_side
    '#default_value': one
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  range:
    '#type': range
    '#title': Range
    '#min': 0
    '#max': 100
    '#step': 1
    '#output': below
    '#output__field_prefix': $
    '#output__field_suffix': '.00'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_rating:
    '#type': webform_rating
    '#title': Rating
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_same:
    '#type': webform_same
    '#title': 'Billing address is the same as the shipping address'
    '#default_value': true
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_scale:
    '#type': webform_scale
    '#title': Scale
    '#min': 1
    '#max': 5
    '#default_value': 1
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  search:
    '#type': search
    '#title': Search
    '#default_value': Loremipsum
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  select:
    '#type': select
    '#title': Select
    '#options':
      one: One
      two: Two
      three: Three
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/1426646">Issue #1426646: "-Select-" option is lost when webform elements uses ''#states''</a><br /><a href="https://www.drupal.org/node/1149078">Issue #1149078: States API doesn''t work with multiple select fields</a><br /><a href="https://www.drupal.org/node/2791741">Issue #2791741: FAPI states: fields aren''t hidden initially when depending on multi-value selection</a>'
    '#default_value': one
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_select_other:
    '#type': webform_select_other
    '#title': 'Select other'
    '#options':
      one: One
      two: Two
      three: Three
    '#default_value': one
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_signature:
    '#type': webform_signature
    '#title': Signature
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  tableselect:
    '#type': tableselect
    '#title': 'Table select'
    '#options':
      one: One
      two: Two
      three: Three
    '#default_value':
      - one
      - two
      - three
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_tableselect_sort:
    '#type': webform_tableselect_sort
    '#title': 'Tableselect sort'
    '#options':
      one: One
      two: Two
      three: Three
    '#default_value':
      - one
      - two
      - three
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_table_sort:
    '#type': webform_table_sort
    '#title': 'Table sort'
    '#options':
      one: One
      two: Two
      three: Three
    '#default_value':
      - one
      - two
      - three
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  tel:
    '#type': tel
    '#title': Telephone
    '#international': true
    '#default_value': '******-333-4444'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_telephone:
    '#type': webform_telephone
    '#title': 'Telephone advanced'
    '#default_value':
      type: Home
      phone: '******-333-4444'
      ext: 0
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_term_checkboxes:
    '#type': webform_term_checkboxes
    '#title': 'Term checkboxes'
    '#vocabulary': tags
    '#default_value':
      - Loremipsum
      - Oratione
      - Dixisset
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_term_select:
    '#type': webform_term_select
    '#title': 'Term select'
    '#vocabulary': tags
    '#default_value': Loremipsum
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_terms_of_service:
    '#type': webform_terms_of_service
    '#title': 'I agree to the {terms of service}.'
    '#required': true
    '#terms_type': slideout
    '#terms_content': '<em>These are the terms of service.</em>'
    '#default_value': true
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  textarea:
    '#type': textarea
    '#title': Textarea
    '#rows': 2
    '#default_value': 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Negat esse eam, inquit, propter se expetendam. Primum Theophrasti, Strato, physicum se voluit; Id mihi magnum videtur. Itaque mihi non satis videmini considerare quod iter sit naturae quaeque progressio. Quare hoc videndum est, possitne nobis hoc ratio philosophorum dare. Est enim tanti philosophi tamque nobilis audacter sua decreta defendere.'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  textfield:
    '#type': textfield
    '#title': 'Text field'
    '#default_value': Loremipsum
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  text_format:
    '#type': text_format
    '#title': 'Text format'
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/997826">Issue #997826: #states doesn''t work correctly with type text_format</a><br /><a href="https://www.drupal.org/node/2625128">Issue #2625128: Text format selection stays visible when using editor and a hidden webform state</a><br /><a href="https://www.drupal.org/node/1954968">Issue #1954968: Required CKEditor fields always fail HTML5 validation</a>'
    '#default_value':
      value: '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Negat esse eam, inquit, propter se expetendam. Primum Theophrasti, Strato, physicum se voluit; Id mihi magnum videtur. Itaque mihi non satis videmini considerare quod iter sit naturae quaeque progressio. Quare hoc videndum est, possitne nobis hoc ratio philosophorum dare. Est enim tanti philosophi tamque nobilis audacter sua decreta defendere.</p>'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_time:
    '#type': webform_time
    '#title': Time
    '#description': '<b>Known Issues:</b><br /><a href="https://www.drupal.org/node/1838234">Issue #1838234: Add jQuery Timepicker for the Time element of the datetime field</a>'
    '#default_value': '09:00'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  url:
    '#type': url
    '#title': URL
    '#default_value': 'http://example.com'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  value:
    '#type': value
    '#title': Value
    '#value': preview
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
  webform_video_file:
    '#type': webform_video_file
    '#title': 'Video file'
    '#states':
      invisible:
        ':input[name="trigger"]':
          checked: true
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: true
  results_disabled_ignore: true
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  debug:
    id: debug
    label: Debug
    notes: ''
    handler_id: debug
    status: true
    conditions: {  }
    weight: 0
    settings:
      format: yaml
      submission: false
variants: {  }
