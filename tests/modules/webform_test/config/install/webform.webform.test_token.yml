uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: '2000-01-01T00:00:00-05:00'
close: '2020-01-01T00:00:00-05:00'
weight: 0
uid: null
template: false
archive: false
id: test_token
title: 'Test: Token'
description: 'Test Webform and Webform Submission tokens'
categories:
  - 'Test: Token'
elements: |
  webform_tokens:
    '#type': webform_codemirror
    '#mode': text
    '#title': 'Webform Tokens'
    '#description': 'Description of <em>webform Tokens</em>'
    '#default_value': |
      Description: [webform:description]
      Webform ID: [webform:id]
      title: [webform:title]
      URL: [webform:url]
      Author: [webform:author]
      Author name: [webform:author:account-name]
      Author roles: [webform:author:roles]
      Open: [webform:open]
      Open long: [webform:open:long]
      Close: [webform:close]
      Close long: [webform:close:long]
      Element title: [webform:element:webform_tokens:title]
      Element description: [webform:element:webform_tokens:description]
      
  webform_submission_tokens:
    '#type': webform_codemirror
    '#mode': text
    '#title': 'Webform Submission Tokens'
    '#default_value': |
      Date changed: [webform_submission:changed]
      Date changed long: [webform_submission:changed:long]
      Date completed: [webform_submission:completed]
      Date completed long: [webform_submission:completed:long]
      Date created: [webform_submission:created]
      Date created long: [webform_submission:created:long]
      Webform: [webform_submission:webform]
      IP address: [webform_submission:ip-address]
      Langcode: [webform_submission:langcode]
      Language: [webform_submission:language]
      In draft: [webform_submission:in-draft]
      State: [webform_submission:state]
      State (label): [webform_submission:state:label]
      Completed page: [webform_submission:completed-page]
      Source URL: [webform_submission:source-url]
      Submitted to: [webform_submission:submitted-to]
      Submitted to ID: [webform_submission:submitted-to:id]
      Submission ID: [webform_submission:sid]
      Submission serial number: [webform_submission:serial]
      Submitter: [webform_submission:user]
      Submitter name: [webform_submission:user:account-name]
      Submitter mail: [webform_submission:user:mail]
      Token: [webform_submission:token]
      Update URL: [webform_submission:token-update-url]
      URL: [webform_submission:url]
      URL Edit Webform: [webform_submission:url:edit-form]
      UUID: [webform_submission:uuid]
      
  webform_submission_source_entity_tokens:
    '#type': webform_codemirror
    '#mode': text
    '#title': 'Webform Submission Source Entity Tokens'
    '#default_value': |
      Author: [webform_submission:source-entity:author]
      Account cancellation URL: [webform_submission:source-entity:author:cancel-url]
      Account Name: [webform_submission:source-entity:author:account-name]
      Created: [webform_submission:source-entity:author:created]
      Display Name: [webform_submission:source-entity:author:display-name]
      Edit URL: [webform_submission:source-entity:author:edit-url]
      Email: [webform_submission:source-entity:author:mail]
      Last login: [webform_submission:source-entity:author:last-login]
      Picture: [webform_submission:source-entity:author:user_picture]
      Roles: [webform_submission:source-entity:author:roles]
      URL: [webform_submission:source-entity:author:url]
      User ID: [webform_submission:source-entity:author:uid]
      Body: [webform_submission:source-entity:body]
      Content ID: [webform_submission:source-entity:nid]
      Content type: [webform_submission:source-entity:content-type]
      Date changed: [webform_submission:source-entity:changed]
      Date created: [webform_submission:source-entity:created]
      Image: [webform_submission:source-entity:field_image]
      Language code: [webform_submission:source-entity:langcode]
      Revision ID: [webform_submission:source-entity:vid]
      Summary: [webform_submission:source-entity:summary]
      Title: [webform_submission:source-entity:title]
      URL: [webform_submission:source-entity:url]
      
  webform_submission_node_tokens:
    '#type': webform_codemirror
    '#mode': text
    '#title': 'Webform Submission Node Tokens'
    '#default_value': |
      Author: [webform_submission:node:author]
      Account cancellation URL: [webform_submission:node:author:cancel-url]
      Account Name: [webform_submission:node:author:account-name]
      Created: [webform_submission:node:author:created]
      Display Name: [webform_submission:node:author:display-name]
      Edit URL: [webform_submission:node:author:edit-url]
      Email: [webform_submission:node:author:mail]
      Last login: [webform_submission:node:author:last-login]
      Picture: [webform_submission:node:author:user_picture]
      Roles: [webform_submission:node:author:roles]
      URL: [webform_submission:node:author:url]
      User ID: [webform_submission:node:author:uid]
      Body: [webform_submission:node:body]
      Content ID: [webform_submission:node:nid]
      Content type: [webform_submission:node:content-type]
      Date changed: [webform_submission:node:changed]
      Date created: [webform_submission:node:created]
      Image: [webform_submission:node:field_image]
      Language code: [webform_submission:node:langcode]
      Revision ID: [webform_submission:node:vid]
      Summary: [webform_submission:node:summary]
      Title: [webform_submission:node:title]
      URL: [webform_submission:node:url]
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: true
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  email:
    id: email
    label: Email
    notes: ''
    handler_id: email
    status: true
    conditions: {  }
    weight: 0
    settings:
      states:
        - completed
      to_mail: _default
      to_options: {  }
      cc_mail: ''
      cc_options: {  }
      bcc_mail: ''
      bcc_options: {  }
      from_mail: _default
      from_options: {  }
      from_name: _default
      subject: _default
      body: '<a href="[webform_submission:token-update-url]">[webform_submission:token-update-url]</a>'
      excluded_elements: {  }
      ignore_access: false
      exclude_empty: true
      exclude_empty_checkbox: false
      exclude_attachments: false
      html: true
      attachments: false
      twig: false
      theme_name: ''
      parameters: {  }
      debug: false
      reply_to: ''
      return_path: ''
      sender_mail: ''
      sender_name: ''
variants: {  }
