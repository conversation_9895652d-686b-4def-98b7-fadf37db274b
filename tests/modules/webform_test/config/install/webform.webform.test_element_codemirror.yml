uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_element_codemirror
title: 'Test: Element: CodeMirror'
description: 'Tests CodeMirror element.'
categories:
  - 'Test: Element'
elements: |
  '#attributes':
    novalidate: novalidate
  text_basic:
    '#type': webform_codemirror
    '#title': text_basic
    '#default_value': Hello
  text_basic_no_wrap:
    '#type': webform_codemirror
    '#title': text_basic_no_wrap
    '#wrap': false
    '#default_value': 'Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo. Quisque sit amet est et sapien ullamcorper pharetra. Vestibulum erat wisi, condimentum sed, commodo vitae, ornare sit amet, wisi. <PERSON><PERSON><PERSON> fermentum, elit eget tincidunt condimentum, eros ipsum rutrum orci, sagittis tempus lacus enim ac dui. Donec non enim in turpis pulvinar facilisis. Ut felis. Praesent dapibus,'
  text_basic_min_max_height:
    '#type': webform_codemirror
    '#title': text_basic_min_max_height
    '#wrap': false
    '#default_value': |
      Pellentesque habitant morbi tristique senectus
      et netus et malesuada fames ac turpis egestas.
      Vestibulum tortor quam, feugiat vitae, ultricies
      eget, tempor sit amet, ante.
      Donec eu libero sit amet quam egestas semper. Aenean
      ultricies mi vitae est. Mauris placerat eleifend leo.
      
    '#attributes':
      style: 'min-height:100px; max-height:200px'
  yaml_basic:
    '#type': webform_codemirror
    '#mode': yaml
    '#title': yaml_basic
    '#default_value': 'test: hello'
  yaml_array:
    '#type': webform_codemirror
    '#mode': yaml
    '#title': yaml_array
    '#description': 'Pass an associative array as the default value'
    '#default_value':
      one: One
      two: Two
      three: Three
  yaml_indexed_array:
    '#type': webform_codemirror
    '#mode': yaml
    '#title': yaml_indexed_array
    '#description': 'Pass an indexed array as the default value'
    '#default_value':
      - one
  yaml_indexed_associative_array:
    '#type': webform_codemirror
    '#mode': yaml
    '#title': yaml_indexed_associative_array
    '#description': 'Pass an indexed associative array as the default value'
    '#default_value':
      - one: One
  yaml_decode_value:
    '#type': webform_codemirror
    '#mode': yaml
    '#title': yaml_decode_value
    '#decode_value': true
    '#description': 'Always decode the value'
    '#default_value': 'test: hello'
  html_basic:
    '#type': webform_codemirror
    '#mode': html
    '#title': html_basic
    '#default_value': '<b>Hello</b>'
  htmlmixed_basic:
    '#type': webform_codemirror
    '#title': htmlmixed_basic
    '#mode': htmlmixed
    '#default_value': |
      <html style="color: green">
        <!-- this is a comment -->
        <head>
          <title>Mixed HTML Example</title>
          <style type="text/css">
            h1 {font-family: comic sans; color: #f0f;}
            div {background: yellow !important;}
            body {
              max-width: 50em;
              margin: 1em 2em 1em 5em;
            }
          </style>
        </head>
        <body>
          <h1>Mixed HTML Example</h1>
          <script>
            function jsFunc(arg1, arg2) {
              if (arg1 && arg2) document.body.innerHTML = "achoo";
            }
          </script>
        </body>
      </html>
      
  twig_basic:
    '#type': webform_codemirror
    '#mode': twig
    '#title': twig_basic
    '#default_value': |
      
      {% set value = "Hello" %}
      {{ value }}
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: message
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: true
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers:
  debug:
    id: debug
    label: Debug
    notes: ''
    handler_id: debug
    status: true
    conditions: {  }
    weight: 1
    settings:
      format: yaml
      submission: false
variants: {  }
