uuid: null
langcode: en
status: open
dependencies:
  enforced:
    module:
      - webform_test_options
open: null
close: null
weight: 0
uid: null
template: false
archive: false
id: test_options
title: 'Test: Options'
description: 'Test webform options.'
categories:
  - 'Test: Options'
elements: |
  general_options:
    '#type': details
    '#title': 'General options'
    '#open': true
    size:
      '#type': select
      '#title': 'Size (size)'
      '#options': size
    time_zones:
      '#type': select
      '#title': 'Time zones (time_zones)'
      '#options': time_zones
    yes_no:
      '#type': select
      '#title': 'Yes/No (yes_no)'
      '#options': yes_no
  bio_options:
    '#type': details
    '#title': 'Biographical options'
    '#open': true
    education:
      '#type': select
      '#title': 'Education (education)'
      '#options': education
    employment_status:
      '#type': select
      '#title': 'Employment status (employment_status)'
      '#options': employment_status
    ethnicity:
      '#type': select
      '#title': 'Ethnicity (ethnicity)'
      '#options': ethnicity
    gender:
      '#type': select
      '#title': 'Gender (gender)'
      '#options': gender
    industry:
      '#type': select
      '#title': 'Industry (industry)'
      '#options': industry
    languages:
      '#type': select
      '#title': 'Languages (languages)'
      '#options': languages
    marital_status:
      '#type': select
      '#title': 'Marital status (marital_status)'
      '#options': marital_status
    phone_types:
      '#type': select
      '#title': 'Phone type (phone_types)'
      '#options': phone_types
    relationship:
      '#type': select
      '#title': 'Relationship (relationship)'
      '#options': relationship
    sex:
      '#type': select
      '#title': 'Sex (sex)'
      '#options': sex
    titles:
      '#type': select
      '#title': 'Titles (titles)'
      '#options': titles
  location_options:
    '#type': details
    '#title': 'Location options'
    '#open': true
    country_codes:
      '#type': select
      '#title': 'Country codes (country_codes)'
      '#options': country_codes
    country_names:
      '#type': select
      '#title': 'Country names (country_names)'
      '#options': country_names
    state_codes:
      '#type': select
      '#title': 'State codes (state_codes)'
      '#options': state_codes
    state_names:
      '#type': select
      '#title': 'State names (state_names)'
      '#options': state_names
    state_province_codes:
      '#type': select
      '#title': 'State/Province codes (state_province_codes)'
      '#options': state_province_codes
    state_province_names:
      '#type': select
      '#title': 'State/Province names (state_province_names)'
      '#options': state_province_names
  date_options:
    '#type': details
    '#title': 'Date options'
    '#open': true
    days:
      '#type': select
      '#title': 'Days (days)'
      '#options': days
    months:
      '#type': select
      '#title': 'Months (months)'
      '#options': months
  likert_options:
    '#type': details
    '#title': 'Likert options'
    '#open': true
    likert_agreement:
      '#type': webform_likert
      '#title': 'Likert: Agreement (likert_agreement)'
      '#questions':
        q1: 'Please answer question 1?'
        q2: 'How about now answering question 2?'
        q3: 'Finally, here is question 3?'
      '#answers': likert_agreement
    likert_comparison:
      '#type': webform_likert
      '#title': 'Likert: Comparison (likert_comparison)'
      '#questions':
        q1: 'Please answer question 1?'
        q2: 'How about now answering question 2?'
        q3: 'Finally, here is question 3?'
      '#answers': likert_comparison
    likert_importance:
      '#type': webform_likert
      '#title': 'Likert: Importance (likert_importance)'
      '#questions':
        q1: 'Please answer question 1?'
        q2: 'How about now answering question 2?'
        q3: 'Finally, here is question 3?'
      '#answers': likert_importance
    likert_quality:
      '#type': webform_likert
      '#title': 'Likert: Quality (likert_quality)'
      '#questions':
        q1: 'Please answer question 1?'
        q2: 'How about now answering question 2?'
        q3: 'Finally, here is question 3?'
      '#answers': likert_quality
    likert_satisfaction:
      '#type': webform_likert
      '#title': 'Likert: Satisfaction (likert_satisfaction)'
      '#questions':
        q1: 'Please answer question 1?'
        q2: 'How about now answering question 2?'
        q3: 'Finally, here is question 3?'
      '#answers': likert_satisfaction
    likert_ten_scale:
      '#type': webform_likert
      '#title': 'Likert: Ten Scale (likert_ten_scale)'
      '#questions':
        q1: 'Please answer question 1?'
        q2: 'How about now answering question 2?'
        q3: 'Finally, here is question 3?'
      '#answers': likert_ten_scale
    likert_would_you:
      '#type': webform_likert
      '#title': 'Likert: Would You (likert_would_you)'
      '#questions':
        q1: 'Please answer question 1?'
        q2: 'How about now answering question 2?'
        q3: 'Finally, here is question 3?'
      '#answers': likert_would_you
  test_options:
    '#type': details
    '#title': 'Test options'
    '#open': true
    test:
      '#type': select
      '#title': 'Test (test)'
      '#options': test
    custom:
      '#type': select
      '#title': 'Custom (custom)'
      '#options': custom
  range_options:
    '#title': 'Range Options'
    '#type': details
    '#open': true
    number_range:
      '#type': select
      '#title': 'Range (1-10)'
      '#options': range
      '#min': 1
      '#max': 10
      '#step': 1
    number_padded_range:
      '#type': select
      '#title': 'Range (001-010)'
      '#options': range
      '#min': 1
      '#max': 10
      '#step': 1
      '#pad_length': 3
      '#pad_string': '0'
    letter_uppercase_range:
      '#type': select
      '#title': 'Range (A-Z)'
      '#options': range
      '#min': A
      '#max': Z
    letter_lowercase_range:
      '#type': select
      '#title': 'Range (a-z)'
      '#options': range
      '#min': a
      '#max': z
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_exception_message: ''
  form_open_message: ''
  form_close_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_reset: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_autofocus: false
  form_details_toggle: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_method: ''
  form_action: ''
  form_attributes: {  }
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_log: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  submission_exception_message: ''
  submission_locked_message: ''
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_title: ''
  confirmation_message: ''
  confirmation_url: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
