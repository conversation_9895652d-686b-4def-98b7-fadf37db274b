<?php

namespace Dr<PERSON>al\webform_test_handler\Plugin\WebformHandler;

use <PERSON><PERSON><PERSON>\webform\Plugin\WebformHandlerBase;
use <PERSON><PERSON>al\webform\Utility\WebformDialogHelper;

/**
 * Webform submission test off-canvas width handler.
 *
 * @WebformHandler(
 *   id = "test_offcanvas_width",
 *   label = @Translation("Test off-canvas width"),
 *   category = @Translation("Testing"),
 *   description = @Translation("Tests handler off-canvas width.")
 * )
 */
class TestWebformOffCanvasWidthHandler extends WebformHandlerBase {

  /**
   * {@inheritdoc}
   */
  public function getOffCanvasWidth() {
    return WebformDialogHelper::DIALOG_WIDE;
  }

}
