langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_webform_test
    - paragraphs.paragraphs_type.webform_test_inline_no_source
  module:
    - webform
id: paragraph.webform_test_inline_no_source.field_webform_test
field_name: field_webform_test
entity_type: paragraph
bundle: webform_test_inline_no_source
label: 'Webform test inline'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:webform'
  handler_settings:
    target_bundles: null
    auto_create: false
field_type: webform
