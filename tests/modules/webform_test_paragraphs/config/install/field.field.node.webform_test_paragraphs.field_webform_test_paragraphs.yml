langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_webform_test_paragraphs
    - node.type.webform_test_paragraphs
    - paragraphs.paragraphs_type.webform_test_inline
    - paragraphs.paragraphs_type.webform_test_inline_no_source
    - paragraphs.paragraphs_type.webform_test_link
    - paragraphs.paragraphs_type.webform_test_nesting
  module:
    - entity_reference_revisions
id: node.webform_test_paragraphs.field_webform_test_paragraphs
field_name: field_webform_test_paragraphs
entity_type: node
bundle: webform_test_paragraphs
label: 'Webform test_paragraphs'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      webform_test_inline: webform_test_inline
      webform_test_inline_no_source: webform_test_inline_no_source
      webform_test_link: webform_test_link
      webform_test_nesting: webform_test_nesting
    negate: 0
    target_bundles_drag_drop:
      webform_test_inline:
        weight: -11
        enabled: true
      webform_test_inline_no_source:
        weight: -10
        enabled: true
      webform_test_link:
        weight: -9
        enabled: true
      webform_test_nesting:
        weight: -7
        enabled: true
field_type: entity_reference_revisions
