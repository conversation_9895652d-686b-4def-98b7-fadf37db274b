langcode: en
status: true
dependencies:
  config:
    - field.field.node.webform_test_paragraphs.body
    - field.field.node.webform_test_paragraphs.field_webform_test_node_value
    - field.field.node.webform_test_paragraphs.field_webform_test_paragraphs
    - field.field.node.webform_test_paragraphs.field_webform_test_value
    - node.type.webform_test_paragraphs
  module:
    - entity_reference_revisions
    - text
    - user
id: node.webform_test_paragraphs.default
targetEntityType: node
bundle: webform_test_paragraphs
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 101
    region: content
  field_webform_test_node_value:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 104
    region: content
  field_webform_test_paragraphs:
    type: entity_reference_revisions_entity_view
    label: hidden
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 102
    region: content
  field_webform_test_value:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 103
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden: {  }
