langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_webform_test
    - paragraphs.paragraphs_type.webform_test_link
  module:
    - webform
id: paragraph.webform_test_link.field_webform_test
field_name: field_webform_test
entity_type: paragraph
bundle: webform_test_link
label: 'Webform test link'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:webform'
  handler_settings:
    target_bundles: null
    auto_create: false
field_type: webform
