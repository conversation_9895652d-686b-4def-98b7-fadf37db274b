langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_webform_test_value
    - node.type.webform_test_paragraphs
id: node.webform_test_paragraphs.field_webform_test_value
field_name: field_webform_test_value
entity_type: node
bundle: webform_test_paragraphs
label: 'Webform test value'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
