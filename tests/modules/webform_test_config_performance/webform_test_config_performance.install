<?php

/**
 * @file
 * Install, update and uninstall functions for the Webform Test Config Performance module.
 */

use <PERSON><PERSON><PERSON>\webform\Entity\Webform;

/**
 * Implements hook_install().
 */
function webform_test_config_performance_install() {
  $elements = [];
  for ($i = 1; $i <= 100; $i++) {
    $index = str_pad($i, 3, '0', STR_PAD_LEFT);
    $elements["element_$index "] = [
      '#type' => 'textfield',
      '#title' => (string) t('Element #@index', ['@index' => $index]),
    ];
  }
  for ($i = 1; $i <= 1000; $i++) {
    $index = str_pad($i, 4, '0', STR_PAD_LEFT);
    $webform = Webform::create([
      'id' => "test_webform_config_$index",
      'title' => (string) t('Test: Config: Webform @index', ['@index' => $index]),
      'elements' => $elements,
      'dependencies' => [
        'enforced' => ['module' => ['webform_test_config_performance']],
      ],
    ]);
    $webform->save();
  }
}
