langcode: en
status: true
dependencies:
  module:
    - webform
  theme:
    - bartik
  enforced:
    module:
      - webform_test_block_submission_limit
id: bartik_webform_test_limit_user
theme: bartik
region: sidebar_second
weight: 8
provider: null
plugin: webform_submission_limit_block
settings:
  id: webform_submission_limit_block
  label: 'Current User Limits'
  provider: webform
  label_display: visible
  type: user
  source_entity: false
  content: ''
  progress_bar: true
  progress_bar_label: '[total] user submission(s)'
  progress_bar_message: '[limit] user limit ([interval])'
  webform_id: ''
  entity_type: ''
  entity_id: ''
visibility: {  }
