langcode: en
status: true
dependencies:
  config:
    - block_content.type.webform
    - field.storage.block_content.body
  module:
    - text
id: block_content.webform.body
field_name: body
entity_type: block_content
bundle: webform
label: Body
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  display_summary: false
field_type: text_with_summary
