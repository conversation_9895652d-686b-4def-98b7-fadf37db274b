langcode: en
status: true
dependencies:
  config:
    - block_content.type.webform
    - field.field.block_content.webform.body
    - field.field.block_content.webform.webform
  module:
    - text
    - webform
id: block_content.webform.default
targetEntityType: block_content
bundle: webform
mode: default
content:
  body:
    label: hidden
    type: text_default
    weight: 0
    settings: {  }
    third_party_settings: {  }
    region: content
  webform:
    weight: 1
    label: hidden
    settings:
      source_entity: 0
    third_party_settings: {  }
    type: webform_entity_reference_entity_view
    region: content
hidden: {  }
