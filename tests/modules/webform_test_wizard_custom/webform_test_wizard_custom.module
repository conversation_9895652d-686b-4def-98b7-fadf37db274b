<?php

/**
 * @file
 * Support module for webform that provides form alter hook for wizard custom tests.
 */

use <PERSON><PERSON>al\Core\Form\FormStateInterface;

/**
 * Implements hook_webform_form_FORM_ID_alter().
 */
function webform_test_wizard_custom_form_webform_submission_test_form_wizard_custom_form_alter(array &$form, FormStateInterface $form_state) {
  $form['#validate'][] = '_webform_test_wizard_custom_form_webform_submission_test_form_wizard_custom_form_validate';
}

/**
 * Validate handler for 'test_form_wizard_custom'.
 */
function _webform_test_wizard_custom_form_webform_submission_test_form_wizard_custom_form_validate(array &$form, FormStateInterface $form_state) {
  if ($form_state->hasAnyErrors()) {
    return;
  }

  // Ge submitted values.
  $values = $form_state->getValues();

  // Alter pages based on selected 'pages'.
  $pages = $form_state->get('pages');
  foreach ($pages as $page_key => &$page) {
    if (isset($form['elements'][$page_key])) {
      $page['#access'] = in_array($page_key, $values['pages']);
    }
  }
  $form_state->set('pages', $pages);
}
