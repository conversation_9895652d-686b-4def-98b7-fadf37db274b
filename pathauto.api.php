<?php

/**
 * @file
 * Documentation for pathauto API.
 */

use Dr<PERSON>al\Core\Language\Language;

/**
 * @todo Update for 8.x-1.x
 *
 * It may be helpful to review some examples of integration from
 * pathauto.pathauto.inc.
 *
 * Pathauto works by using tokens in path patterns.  Thus the simplest
 * integration is just to provide tokens.  Token support is provided by Drupal
 * core. To provide additional token from your module, implement the following
 * hooks:
 *
 * hook_tokens() - http://api.drupal.org/api/function/hook_tokens
 * hook_token_info() - http://api.drupal.org/api/function/hook_token_info
 *
 * If you wish to provide pathauto integration for custom paths provided by your
 * module, there are a few steps involved.
 *
 * 1. hook_pathauto()
 *    Provide information required by pathauto for the settings form as well as
 *    bulk generation.  See the documentation for hook_pathauto() for more
 *    details.
 *
 * 2. pathauto_create_alias()
 *    At the appropriate time (usually when a new item is being created for
 *    which a generated alias is desired), call pathauto_create_alias() with the
 *    appropriate parameters to generate and create the alias. See the user,
 *    taxonomy, and node hook implementations in pathauto.module for examples.
 *    Also see the documentation for pathauto_create_alias().
 *
 * 3. pathauto_path_delete_all()
 *    At the appropriate time (usually when an item is being deleted), call
 *    pathauto_path_delete_all() to remove any aliases that were created for the
 *    content being removed.  See the documentation for
 *    pathauto_path_delete_all() for more details.
 *
 * 4. hook_path_alias_types()
 *    For modules that create new types of content that can be aliased with
 *    pathauto, a hook implementation is needed to allow the user to delete them
 *    all at once.  See the documentation for hook_path_alias_types() below for
 *    more information.
 *
 * There are other integration points with pathauto, namely alter hooks that
 * allow you to change the data used by pathauto at various points in the
 * process.  See the below hook documentation for details.
 */

/**
 * Alter pathauto alias type definitions.
 *
 * @param array &$definitions
 *   Alias type definitions.
 */
function hook_path_alias_types_alter(array &$definitions) {
}

/**
 * Determine if a possible URL alias would conflict with any existing paths.
 *
 * Returning TRUE from this function will trigger pathauto_alias_uniquify() to
 * generate a similar URL alias with a suffix to avoid conflicts.
 *
 * @param string $alias
 *   The potential URL alias.
 * @param string $source
 *   The source path for the alias (e.g. 'node/1').
 * @param string $langcode
 *   The language code for the alias (e.g. 'en').
 *
 * @return bool
 *   TRUE if $alias conflicts with an existing, reserved path, or FALSE/NULL if
 *   it does not match any reserved paths.
 *
 * @see pathauto_alias_uniquify()
 */
function hook_pathauto_is_alias_reserved($alias, $source, $langcode) {
  // Check our module's list of paths and return TRUE if $alias matches any of
  // them.
  return (bool) \Drupal::database()->query("SELECT 1 FROM {mytable} WHERE path = :path", [':path' => $alias])->fetchField();
}

/**
 * Alter the pattern to be used before an alias is generated by Pathauto.
 *
 * This hook will only be called if a default pattern is configured (on
 * admin/config/search/path/patterns).
 *
 * @param \Drupal\pathauto\PathautoPatternInterface $pattern
 *   The Pathauto pattern to be used.
 * @param array $context
 *   An associative array of additional options, with the following elements:
 *   - 'module': The module or entity type being aliased.
 *   - 'op': A string with the operation being performed on the object being
 *     aliased. Can be either 'insert', 'update', 'return', or 'bulkupdate'.
 *   - 'source': A string of the source path for the alias (e.g. 'node/1').
 *   - 'data': An array of keyed objects to pass to token_replace().
 *   - 'bundle': The sub-type or bundle of the object being aliased.
 *   - 'language': A string of the language code for the alias (e.g. 'en').
 *     This can be altered by reference.
 */
function hook_pathauto_pattern_alter(\Drupal\pathauto\PathautoPatternInterface $pattern, array $context) {
  // Switch out any [node:created:*] tokens with [node:updated:*] on update.
  if ($context['module'] == 'node' && ($context['op'] == 'update')) {
    $pattern->setPattern(preg_replace('/\[node:created(\:[^]]*)?\]/', '[node:updated$1]', $pattern->getPattern()));
  }
}

/**
 * Alter Pathauto-generated aliases before saving.
 *
 * @param string $alias
 *   The automatic alias after token replacement and strings cleaned.
 * @param array $context
 *   An associative array of additional options, with the following elements:
 *   - 'module': The module or entity type being aliased.
 *   - 'op': A string with the operation being performed on the object being
 *     aliased. Can be either 'insert', 'update', 'return', or 'bulkupdate'.
 *   - 'source': A string of the source path for the alias (e.g. 'node/1').
 *     This can be altered by reference.
 *   - 'data': An array of keyed objects to pass to token_replace().
 *   - 'type': The sub-type or bundle of the object being aliased.
 *   - 'language': A string of the language code for the alias (e.g. 'en').
 *     This can be altered by reference.
 *   - 'pattern': A string of the pattern used for aliasing the object.
 */
function hook_pathauto_alias_alter(&$alias, array &$context) {
  // Add a suffix so that all aliases get saved as 'content/my-title.html'.
  $alias .= '.html';

  // Force all aliases to be saved as language neutral.
  $context['language'] = Language::LANGCODE_NOT_SPECIFIED;
}

/**
 * Alter the list of punctuation characters for Pathauto control.
 *
 * @param array $punctuation
 *   An array of punctuation to be controlled by Pathauto during replacement
 *   keyed by punctuation name. Each punctuation record should be an array
 *   with the following key/value pairs:
 *   - value: The raw value of the punctuation mark.
 *   - name: The human-readable name of the punctuation mark. This must be
 *     translated using t() already.
 */
function hook_pathauto_punctuation_chars_alter(array &$punctuation) {
  // Add the trademark symbol.
  $punctuation['trademark'] = ['value' => '™', 'name' => t('Trademark symbol')];

  // Remove the dollar sign.
  unset($punctuation['dollar']);
}
